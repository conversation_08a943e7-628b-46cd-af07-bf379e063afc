import dotenv from "dotenv";
import { FormDataEncoder } from "form-data-encoder";
import { Blob, FormData } from "formdata-node"; // You can use `File` from fetch-blob >= 3.x
// Note that `node-fetch` >= 3.x have builtin support for spec-compliant FormData, sou you'll only need the `form-data-encoder` if you use `node-fetch` <= 2.x.
import fetch from "node-fetch";
import { Readable } from "stream";

dotenv.config();

async function main() {
  const form = new FormData();
  const imgResponse = await fetch("http://www.fillmurray.com/g/155/300");
  const blob = new Blob([await imgResponse.buffer()], { type: "image/png" });
  form.set("files[]", blob, "file.png");

  const encoder = new FormDataEncoder(form);

  const options = {
    method: "post",
    headers: {
      ...encoder.headers,
      "Api-Key": process.env.DISCOURSE_API_KEY as string,
      "Api-Username": "system",
    },
    body: Readable.from(encoder),
  };

  const url =
    "https://forum.plasmic.app/uploads.json?type=composer&synchronous=true";
  const response = await fetch(url, options);

  console.log(await response.json());
}

main();
