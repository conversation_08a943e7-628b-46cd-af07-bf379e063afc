/** @format */

import {
  extractPlasmicQueryDataFromElement,
  GlobalVariantSpec,
  initPlasmicLoader,
  renderToString,
} from "https://esm.sh/@plasmicapp/loader-react";

import * as ReactDOMServer from "https://esm.sh/react-dom@17.0.2/server";
import * as React from "https://esm.sh/react@17.0.2";

function getCodegenUrl() {
  return "https://studio.plasmic.app";
}

export async function genLoaderHtmlBundle(opts: {
  projectId: string;
  component: string;
  projectToken: string;
  version?: string;
  hydrate?: boolean;
  embedHydrate?: boolean;
  prepass?: boolean;
  componentProps?: any;
  globalVariants?: GlobalVariantSpec[];
}) {
  const {
    projectId,
    component,
    version,
    hydrate,
    embedHydrate,
    projectToken,
    componentProps,
    globalVariants,
    prepass,
  } = opts;

  const loader = initPlasmicLoader({
    projects: [
      {
        id: projectId,
        version,
        token: projectToken,
      },
    ],
    preview: !version,
    host: getCodegenUrl(),
  });

  const data = await loader.fetchComponentData({
    name: component,
    projectId,
  });

  const prefetchedQueryData = prepass
    ? await extractPlasmicQueryDataFromElement(
        loader,
        { name: component, projectId },
        {
          prefetchedData: data,
          componentProps,
          globalVariants,
        }
      )
    : undefined;

  const innerHtml = renderToString(
    loader,
    { name: component, projectId },
    {
      prefetchedData: data,
      componentProps,
      globalVariants,
      prefetchedQueryData,
    }
  );

  const outerElement = React.createElement(
    React.Fragment,
    {},
    React.createElement("div", {
      "data-plasmic-project-id": projectId,
      "data-plasmic-project-version": version,
      "data-plasmic-component": component,
      "data-plasmic-project-token":
        hydrate && !embedHydrate ? projectToken : "",
      "data-plasmic-component-data":
        hydrate && embedHydrate ? JSON.stringify(data) : "",
      "data-plasmic-component-props": JSON.stringify(componentProps || {}),
      "data-plasmic-global-variants": JSON.stringify(globalVariants || []),
      "data-plasmic-prefetched-query-data":
        hydrate && embedHydrate && prepass
          ? JSON.stringify(prefetchedQueryData || {})
          : "",
      dangerouslySetInnerHTML: { __html: innerHtml },
    }),
    hydrate &&
      React.createElement("script", {
        async: true,
        src: `${getCodegenUrl()}/static/js/loader-hydrate.js`,
      })
  );

  const outerHtml = ReactDOMServer.renderToStaticMarkup(outerElement);

  return {
    html: outerHtml,
  };
}

async function main() {
  console.log(
    (self.process = {
      env: {
        NODE_ENV: "production",
      },
    })
  );
  const { html } = await genLoaderHtmlBundle({
    projectId: "45vSSze9ZFHqTsS1VazppN",
    component: "Homepage",
    projectToken:
      "qe3Yo0g4ZgIV9RjwxfpZYyAfacTB7e9UTbZ2nvhDatebJ8ERjW73m3qC5OErbJPQYnQyWmUnpgHppNktHQKNg",
  });
  console.log(html);
}

main();
