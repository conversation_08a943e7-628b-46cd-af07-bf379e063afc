# Getting started

## Install

1. Open Chrome
2. Go to chrome://extensions/
3. Enable "Developer mode" on the top right
4. Click "Load unpacked" on the top left
5. Choose this directory

## Try it

1. Visit a Plasmic website, like https://www.plasmic.app/.
2. Click the "Plasmic Jump Tool" extension (you might want to pin it!). Plasmic elements should now be outlined.
3. Click a Plasmic element. A dialog with the current element and its ancestors should appear.
4. Click the component to jump to in Plasmic Studio!

# Dev Notes

## jquery-ui.css / jquery-ui.min.css modifications

* Prepend `background-image` URLs with `chrome-extension://__MSG_@@extension_id__`.
* Add `z-index: 2147483647` to `.ui-dialog`.
