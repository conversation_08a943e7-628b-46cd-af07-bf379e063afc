apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: arc-runner-set-migrate-bundles
spec:
  chart:
    spec:
      chart: gha-runner-scale-set
      version: '0.6.1'
      sourceRef:
        kind: HelmRepository
        name: actions-runner-controller
        namespace: flux-system
  interval: 10m
  valuesFrom:
    - kind: Secret
      name: arc-runner-secrets
  values:
    ## githubConfigUrl is the GitHub url for where you want to configure runners
    ## ex: https://github.com/myorg/myrepo or https://github.com/myorg
    githubConfigUrl: "https://github.com/plasmicapp/plasmic-internal"

    maxRunners: 16

    minRunners: 0

    runnerGroup: "default"

    # name of the runner scale set to create.  Defaults to the helm release name
    runnerScaleSetName: "arc-runner-set-migrate-bundles"
    containerMode:
      type: "dind"

    template:
      metadata:
        labels:
          app: runner
      spec:
        containers:
        - name: runner
          image: plasmicapp/arc-runner-base:latest
          command: ["/home/<USER>/run.sh"]
          resources:
            requests:
              cpu: "4"
              memory: "16Gi"
        tolerations:
        - key: "migrate-bundles"
          operator: "Equal"
          effect: "NoExecute"
          value: "true"
        affinity:
          podAntiAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - runner
              topologyKey: "kubernetes.io/hostname"
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                  - key: migrate-bundles
                    operator: In
                    values:
                    - "true"
