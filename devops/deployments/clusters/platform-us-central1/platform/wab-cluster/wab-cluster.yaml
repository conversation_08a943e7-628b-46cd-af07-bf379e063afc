kind: "postgresql"
apiVersion: "acid.zalan.do/v1"
metadata:
  name: "postgres-wab-cluster"
  namespace: "platform"
  labels:
    team: acid
spec:
  teamId: "acid"
  postgresql:
    version: "15"
    parameters:
      wal_keep_segments: "200"
      work_mem: "8192"
  numberOfInstances: 2
  allowedSourceRanges:
    - 0.0.0.0/0
  users:
    wab: []
    superwab:
    - superuser
    - createdb
  databases:
    wab: wab
  usersIgnoringSecretRotation:
    - wab
    - suberwab
  resources:
    requests:
      cpu: "3"
      memory: 16Gi
    limits:
      memory: 30Gi
  volume:
    size: "4096G"
    storageClass: "premium-rwo"
  sidecars:
    - name: prom-exporter
      image: quay.io/prometheuscommunity/postgres-exporter:v0.17.1
      resources:
        limits:
          cpu: 500m
          memory: 500Mi
        requests:
          cpu: 100m
          memory: 100Mi
      env:
        - name: DATA_SOURCE_URI
          value: localhost:5432/postgres?sslmode=disable
        - name: DATA_SOURCE_USER
          value: $(POSTGRES_USER)
        - name: DATA_SOURCE_PASS
          value: $(POSTGRES_PASSWORD)
      ports:
        - containerPort: 9187
      readinessProbe:
        httpGet:
          path: /metrics
          port: 9187
        initialDelaySeconds: 5
        periodSeconds: 5
        timeoutSeconds: 3
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: workload-type
          operator: In
          values:
          - database
        - key: cloud.google.com/machine-family
          operator: In
          values:
          - c3
  tolerations:
  - key: workload-type
    operator: Equal
    value: database
    effect: NoSchedule
  env:
  - name: ALLOW_NOSSL
    value: "true"
