apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: velero
spec:
  chart:
    spec:
      chart: velero
      version: '8.3.0'
      sourceRef:
        kind: HelmRepository
        name: velero
        namespace: flux-system
  interval: 10m
  values:
    credentials:
      useSecret: false
    serviceAccount:
      server:
        annotations:
          iam.gke.io/gcp-service-account: <EMAIL>
    deployNodeAgent: true
    configuration:
      features: EnableCSI
      backupStorageLocation:
      - name: default
        provider: velero.io/gcp
        bucket: plasmic-dev-velero
        default: true
        config:
          serviceAccount: <EMAIL>
      volumeSnapshotLocation:
      - name: gcp
        provider: velero.io/gcp
    initContainers:
    - name: velero-plugin-for-gcp
      image: velero/velero-plugin-for-gcp:v1.11.0
      imagePullPolicy: IfNotPresent
      volumeMounts:
      - mountPath: /target
        name: plugins

