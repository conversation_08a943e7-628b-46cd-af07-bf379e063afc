{{- if and .Values.worker.enabled .Values.worker.hpa.enabled -}}
{{- range .Values.worker.consumers }}
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ template "posthog.fullname" $ }}-worker-{{ .name }}
  labels: {{- include "_snippet-metadata-labels-common" $ | nindent 4 }}
spec:
  scaleTargetRef:
    kind: Deployment
    apiVersion: apps/v1
    name: {{ template "posthog.fullname" $ }}-worker-{{ .name }}
  minReplicas: {{ ( .hpa | default dict ).minpods | default $.Values.worker.hpa.minpods  }}
  maxReplicas: {{ ( .hpa | default dict ).maxpods | default $.Values.worker.hpa.maxpods  }}
  metrics:
  {{- with $.Values.worker.hpa.cputhreshold }}
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ . }}
  {{- end }}
  behavior: 
    {{ toYaml $.Values.worker.hpa.behavior | nindent 4 }}
{{- end }}
{{- end }}
