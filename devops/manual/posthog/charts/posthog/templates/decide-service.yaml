{{- if .Values.decide.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "posthog.fullname" . }}-decide
  annotations: {{- include "_snippet-metadata-annotations-common" . | nindent 4 }}
   {{- range $key, $value := .Values.service.annotations }}
     {{ $key }}: {{ $value | quote }}
   {{- end }}
  labels: {{- include "_snippet-metadata-labels-common" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
  - port: {{ .Values.service.externalPort }}
    targetPort: {{ .Values.service.internalPort }}
    protocol: TCP
    name: {{ .Values.service.name }}
{{- if and (.Values.service.nodePort) (eq .Values.service.type "NodePort") }}
    nodePort: {{ .Values.service.nodePort }}
{{- end }}
{{- if .Values.service.externalIPs }}
  externalIPs:
{{ toYaml .Values.service.externalIPs | indent 4 }}
{{- end }}
  selector:
    app: {{ template "posthog.fullname" . }}
    role: decide
  {{- with .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
