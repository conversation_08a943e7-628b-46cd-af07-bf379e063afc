{{- if .Values.clickhouse.enabled }}
# Template Parameters:
#
# NAMESPACE={{ .Values.clickhouse.namespace | default .Release.Namespace }}
# COMMENT=#
# ROLE_KIND=ClusterRole
# ROLE_NAME=clickhouse-operator-{{ .Values.clickhouse.namespace | default .Release.Namespace }}
# ROLE_BINDING_KIND=ClusterRoleBinding
# ROLE_BINDING_NAME=clickhouse-operator-{{ .Values.clickhouse.namespace | default .Release.Namespace }}
#
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: clickhouse-operator-{{ .Values.clickhouse.namespace | default .Release.Namespace }}
  namespace: {{ .Values.clickhouse.namespace | default .Release.Namespace }}
  labels:
    clickhouse.altinity.com/chop: 0.18.4
rules:
- apiGroups:
    - ""
  resources:
    - configmaps
    - services
  verbs:
    - get
    - list
    - patch
    - update
    - watch
    - create
    - delete
- apiGroups:
    - ""
  resources:
    - endpoints
  verbs:
    - get
    - list
    - watch
- apiGroups:
    - ""
  resources:
    - events
  verbs:
    - create
- apiGroups:
    - ""
  resources:
    - persistentvolumeclaims
  verbs:
    - get
    - list
    - patch
    - update
    - watch
    - delete
- apiGroups:
    - ""
  resources:
    - persistentvolumes
    - pods
  verbs:
    - get
    - list
    - patch
    - update
    - watch
- apiGroups:
    - apps
  resources:
    - statefulsets
  verbs:
    - get
    - list
    - patch
    - update
    - watch
    - create
    - delete
- apiGroups:
    - apps
  resources:
    - replicasets
  verbs:
    - get
    - patch
    - update
    - delete
- apiGroups:
    - apps
  resourceNames:
    - clickhouse-operator
  resources:
    - deployments
  verbs:
    - get
    - patch
    - update
    - delete
- apiGroups:
    - policy
  resources:
    - poddisruptionbudgets
  verbs:
    - get
    - list
    - patch
    - update
    - watch
    - create
    - delete
- apiGroups:
    - clickhouse.altinity.com
  resources:
    - clickhouseinstallations
  verbs:
    - get
    - patch
    - update
    - delete
- apiGroups:
    - clickhouse.altinity.com
  resources:
    - clickhouseinstallations
    - clickhouseinstallationtemplates
    - clickhouseoperatorconfigurations
  verbs:
    - get
    - list
    - watch
- apiGroups:
    - clickhouse.altinity.com
  resources:
    - clickhouseinstallations/finalizers
    - clickhouseinstallationtemplates/finalizers
    - clickhouseoperatorconfigurations/finalizers
  verbs:
    - update
- apiGroups:
    - clickhouse.altinity.com
  resources:
    - clickhouseinstallations/status
    - clickhouseinstallationtemplates/status
    - clickhouseoperatorconfigurations/status
  verbs:
    - get
    - update
    - patch
    - create
    - delete
- apiGroups:
    - ""
  resources:
    - secrets
  verbs:
    - get
    - list
- apiGroups:
    - apiextensions.k8s.io
  resources:
    - customresourcedefinitions
  verbs:
    - get
    - list

{{- end }}
