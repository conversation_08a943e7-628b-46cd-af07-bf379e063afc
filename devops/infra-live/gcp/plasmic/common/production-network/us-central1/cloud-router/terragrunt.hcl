locals {
  org_variables     = read_terragrunt_config(find_in_parent_folders("globals.hcl"))
  env_variables     = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  project_variables = read_terragrunt_config(find_in_parent_folders("project.hcl"))
  region_vars       = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  project_id        = local.project_variables.locals.project_id
  region            = local.region_vars.locals.region
  prefix            = local.org_variables.locals.prefix_prod
  prefix_dev        = local.org_variables.locals.prefix_dev
  subnet_name       = "${local.region}/${local.prefix}-${local.region}"
}

terraform {
  source = "tfr:///terraform-google-modules/cloud-router/google//?version=6.2.0"
}

include {
  path = find_in_parent_folders()
}

dependency "networking" {
  config_path = "../../global/vpc"
}

dependency "networking_project" {
  config_path = "../../project"
}


inputs = {
  name    = "${local.prefix}-${local.region}"
  project = local.project_id
  network = dependency.networking.outputs.network_name
  region  = local.region
  bgp     = {
    asn               = "65001"
    advertise_mode    = "CUSTOM"
    advertised_groups = ["ALL_SUBNETS"]
  }

  nats = [{
    name                               = "${local.prefix}"
    source_subnetwork_ip_ranges_to_nat = "LIST_OF_SUBNETWORKS"
    subnetworks = [
      {
        name                     = dependency.networking.outputs.subnets_names[1]
        source_ip_ranges_to_nat  = ["PRIMARY_IP_RANGE", "LIST_OF_SECONDARY_IP_RANGES"]
        secondary_ip_range_names = dependency.networking.outputs.subnets["${local.subnet_name}"].secondary_ip_range[*].range_name
      }
    ]
  }]
}
