locals {
  project              = read_terragrunt_config(find_in_parent_folders("project.hcl"))
  project_id           = local.project.locals.project_id
}

terraform {
  source = "tfr:///terraform-google-modules/service-accounts/google//?version=4.4.3"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  project_id          = local.project_id
  names               = ["otel-collector"]
  project_roles       = [
    "${local.project_id}=>roles/cloudtrace.agent"]
  display_name        = "Otel collector Service Account"
  description         = "Otel collector Service Account to send traces to Monitoring"
}
