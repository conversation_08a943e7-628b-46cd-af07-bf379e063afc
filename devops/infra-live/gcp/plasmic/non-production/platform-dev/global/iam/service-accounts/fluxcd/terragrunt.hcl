locals {
  project              = read_terragrunt_config(find_in_parent_folders("project.hcl"))
  project_id           = local.project.locals.project_id
}

terraform {
  source = "tfr:///terraform-google-modules/service-accounts/google//?version=4.4.3"
}

include {
  path = find_in_parent_folders()
}

dependency "registries_project" {
  config_path = "../../../../../../common/registries/project"
}

inputs = {
  project_id          = local.project_id
  names               = ["fluxcd-dev"]
  project_roles       = [
    "${local.project_id}=>roles/cloudkms.cryptoKeyDecrypter",
    "${dependency.registries_project.outputs.project_id}=>roles/artifactregistry.reader"]
  display_name        = "GKE Dev FluxCD Service Account"
  description         = "GKE Dev FluxCD Service Account to manage Kubernetes"
}
