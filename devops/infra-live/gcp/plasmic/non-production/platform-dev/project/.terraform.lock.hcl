# This file is maintained automatically by "tofu init".
# Manual edits may be lost in future updates.

provider "registry.opentofu.org/hashicorp/google" {
  version     = "6.19.0"
  constraints = ">= 3.43.0, >= 4.28.0, >= 5.41.0, < 7.0.0"
  hashes = [
    "h1:UsAxFqYNbF8ZZntP2KIPjlITSM9Bwb8y/oQg3+UAZAc=",
    "zh:02c27ececc39c7b78b0c61059e0bc237520f4550ae6238c117f3bd32ea4d5ba2",
    "zh:40b6a82a0159d90456cf0b1b209976509e89b135936ef60b6fff5f453e051543",
    "zh:593da5017b7561604d5bc76e5c1d4eb83497e9e68d45206b3c724f7de9b86f4c",
    "zh:7aa208b75dd4bf5055598dbadd5faa7f218fa111410ecb0d2c294bdef616cd67",
    "zh:90b3f8e2476b03a24145a4de1fbb07120b3627a1a4f2aa4f91ffd9d50c23e0e3",
    "zh:b8e10e5a4104345b0b15faa82f0675e61486bcf2f1ac7a53c03afcbd9c9f11b3",
    "zh:ba07f84e05d78a82955e3f1cdae29274d01b01da066a07a3d51926f5f1ec1d6d",
    "zh:c6588453ec3b0e1daf7c1e9c2f8893486d64a14bbb8b29b7ec5ee341f3cd6a8c",
    "zh:df68deee894b9f2267f8718a76b8ab74d6bc067c8bd1c8ce2bfc8fcb83077e40",
    "zh:f60e1d46f05526579fb21f7bf1a079fbf685ad35f0e7feccb3e4921b4a4a123a",
  ]
}

provider "registry.opentofu.org/hashicorp/google-beta" {
  version     = "6.19.0"
  constraints = ">= 3.43.0, >= 4.11.0, >= 5.41.0, < 7.0.0"
  hashes = [
    "h1:7Tnpi/pQqZZi9fppPaf+o/4iLOZ6YgSKue6Y8QnfWNU=",
    "zh:11dffbd8f4a33f6e3d2fb8ed62cec153aac0b3a1b6ad07f580485512c8b75eeb",
    "zh:27f1ac45647e398df332aab0916e9a437df3ef3d8f3c84d877c5fb90b93b9a90",
    "zh:3983641f375bc4e2aa90cd51e453bf47600872ab5d031a41c4df80754808db80",
    "zh:479eee6681d0bc522c1ceeba0f7eacf2855f8d653cb8779c77c9c4dbf9fcb318",
    "zh:596b42fb7403d520fef1bdf60e0db917b82362fb0f7e2a1fe3a7655c2df84bcb",
    "zh:5e436087aad1b3a92167f9095d65b74015d32b4419b6c08ba2fef2e7aa2ad451",
    "zh:6729349a98a6580afa2e5e2117e514ecc4590cb44d8b78a0a7b57be239959b52",
    "zh:a0bde41dce644fc3a8e809a19f7d0e67db5914d4449e2e95cba976021f912eb5",
    "zh:ad5b1fa92e107b45d6f94ac644d350820d8430866ec066a0e6cf83ec6a4ecf2b",
    "zh:c924a5f6ffbb5f7c7b9f74f6e3946ba5065c871c18398f163c5b8954dd6fe289",
  ]
}

provider "registry.opentofu.org/hashicorp/null" {
  version     = "3.2.3"
  constraints = ">= 2.1.0"
  hashes = [
    "h1:LN7WjQlMDIYGsXlum1kvMk5M8XzS2gzPTHmbEkxB6B0=",
    "zh:1d57d25084effd3fdfd902eca00020b34b1fb020253b84d7dd471301606015ac",
    "zh:65b7f9799b88464d9c2ec529713b7f52ea744275b61a8dc86cdedab1b2dcb933",
    "zh:80d3e9c95b7b4ae7c54005cd127cae82e5c53d2b7023ef24c147337bac9dadd9",
    "zh:841b60c07683e4bf456799ccd718896fdafdcc2c49252ae09967f2e74d8c8a03",
    "zh:8fa1c592a9c78222e35713c6edb3f1f818a4c6f3524a30a209f0a7e919827b68",
    "zh:bb795cc1429e09466840c09d39a28edf1db5070b1ec76822fc1173906a264572",
    "zh:da1784818a89bea29dfe660632f0060a7a843e4e564d74435fbeca002b0f7d2a",
    "zh:f409bf21b1cdaa6dac47cd79806f3d93f67e9507fe4dbf33b0165335f53bc2e1",
    "zh:fbea7a1ff84b430ba9594698e93196d81d03e4036de3d1cafccb2a96d5b38581",
    "zh:fbf0c84663a7e85881388d7d71ac862184f05fbf2d17ecf76bc5d3d7503ea260",
  ]
}

provider "registry.opentofu.org/hashicorp/random" {
  version     = "3.6.3"
  constraints = ">= 2.2.0"
  hashes = [
    "h1:32/UZofQoXk8zPj9vpIDiSEmERA3Mx2VPvk1lHTTHvw=",
    "zh:1bfd2e54b4eee8c761a40b6d99d45880b3a71abc18a9a7a5319204da9c8363b2",
    "zh:21a15ac74adb8ba499aab989a4248321b51946e5431219b56fc827e565776714",
    "zh:221acfac3f7a5bcd6cb49f79a1fca99da7679bde01017334bad1f951a12d85ba",
    "zh:3026fcdc0c1258e32ab519df878579160b1050b141d6f7883b39438244e08954",
    "zh:50d07a7066ea46873b289548000229556908c3be746059969ab0d694e053ee4c",
    "zh:54280cdac041f2c2986a585f62e102bc59ef412cad5f4ebf7387c2b3a357f6c0",
    "zh:632adf40f1f63b0c5707182853c10ae23124c00869ffff05f310aef2ed26fcf3",
    "zh:b8c2876cce9a38501d14880a47e59a5182ee98732ad7e576e9a9ce686a46d8f5",
    "zh:f27e6995e1e9fe3914a2654791fc8d67cdce44f17bf06e614ead7dfd2b13d3ae",
    "zh:f423f2b7e5c814799ad7580b5c8ae23359d8d342264902f821c357ff2b3c6d3d",
  ]
}

provider "registry.opentofu.org/hashicorp/time" {
  version     = "0.12.1"
  constraints = ">= 0.5.0"
  hashes = [
    "h1:D4eN1hzoSjOkkBg1dD13M5bzppQWosH/tkqYkeKjQks=",
    "zh:50a9b67d5f5f42adbdb7712f67858aa64b5670070f6710751239b535fb48a4df",
    "zh:5a846fae035e363aed75b966d64a56f3489a38083e8407aaa656730437f53ed7",
    "zh:6767f1fc8a679b48eaa4cd114da0d8185fb3546375f3a0fb3728f10fa3dbc551",
    "zh:85d3da407c828bf057cbc0e86c75ef3d0f9f74a73c4ea1b4aef18e33f41092b1",
    "zh:9180721325139431112c638f5382a740ff219782f81d6346cdff5bccc418a43f",
    "zh:9ba9989f905a64db1409a9a57649549c89c7aedfb55ae399a7fa9411aafaadac",
    "zh:b3d9e7afb6a742e9be0541bc434b00d849fdfab0b4b859ceb0296c26c541af15",
    "zh:c87da712d718acd9dd03f544b020c320699cb29df197be4f74783e3c3d80fc17",
    "zh:cb1abe07638ef6d7b41d0e86dfb12d60a513aca3395a5da7191947f7459821dd",
    "zh:ecff2e823ef49eda03663fa8ee8bdc17d27cd419dbdacbf1719f38812dbf417e",
  ]
}
