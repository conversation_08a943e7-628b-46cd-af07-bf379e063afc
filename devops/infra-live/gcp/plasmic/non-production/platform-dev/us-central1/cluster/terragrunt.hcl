locals {
  org         = read_terragrunt_config(find_in_parent_folders("globals.hcl"))
  env         = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  project     = read_terragrunt_config(find_in_parent_folders("project.hcl"))
  region_vars = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  project_id  = local.project.locals.project_id
  region      = local.region_vars.locals.region
}

terraform {
  source = "tfr:///terraform-google-modules/kubernetes-engine/google//?version=35.0.0"
}

include {
  path = find_in_parent_folders()
}

dependency "networking" {
  config_path = "../../../../common/production-network/global/vpc"
}

dependency "networking_project" {
  config_path = "../../../../common/production-network/project"
}

dependency "project" {
  config_path = "../../project"
}

dependency "gke_node_sa" {
  config_path = "../../global/iam/service-accounts/gke-node"
}

inputs = {
  name               = "${dependency.project.outputs.project_name}-${local.region}"
  project_id         = local.project_id
  region             = local.region
  zones              = ["${local.region}-a"]
  network_project_id = dependency.networking_project.outputs.project_id
  network            = dependency.networking.outputs.network_name
  subnetwork         = dependency.networking.outputs.subnets_names[0]
  ip_range_pods      = dependency.networking.outputs.subnets_secondary_ranges[0][0].range_name
  ip_range_services  = dependency.networking.outputs.subnets_secondary_ranges[0][1].range_name
  master_authorized_networks = [
    {
      cidr_block   = "************/32"
      display_name = "WireGuard VPN"
    },
  ]
  network_tags       = ["gke-platform-dev-us-central1"]
  default_max_pods_per_node            = "20"
  gateway_api_channel                  = "CHANNEL_STANDARD"
  gce_pd_csi_driver                    = true
  enable_cost_allocation               = true
  monitoring_enable_managed_prometheus = true
  logging_enabled_componentes          = ["SYSTEM_COMPONENTS", "APISERVER", "SCHEDULER", "CONTROLLER_MANAGER", "WORKLOADS"]
  monitoring_enabled_components        = ["SYSTEM_COMPONENTS", "APISERVER", "SCHEDULER", "CONTROLLER_MANAGER"]
  remove_default_node_pool             = true
  cluster_autoscaling = {
    "enabled" : true,
    "auto_repair" : true,
    "auto_upgrade" : true,
    "autoscaling_profile" : "OPTIMIZE_UTILIZATION",
    "disk_size" : 40,
    "disk_type" : "pd-standard",
    "enable_integrity_monitoring" : true,
    "enable_secure_boot" : true,
    "image_type" : "COS_CONTAINERD",
    "max_cpu_cores" : 20,
    "max_memory_gb" : 256,
    "min_cpu_cores" : 0,
    "min_memory_gb" : 0,
    "gpu_resources" : []
  }
}
