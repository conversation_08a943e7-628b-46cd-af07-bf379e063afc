!function(){"use strict";function e(e,n){void 0===n&&(n={});var t=n.insertAt;if(e&&"undefined"!=typeof document){var i=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css","top"===t&&i.firstChild?i.insertBefore(a,i.firstChild):i.appendChild(a),a.styleSheet?a.styleSheet.cssText=e:a.appendChild(document.createTextNode(e))}}e(":where(.__wab_flex-container),\n:where(.ρfc) {\n  display: flex;\n  flex: 1;\n  align-self: stretch;\n  pointer-events: none;\n}\n\n:where(.__wab_flex-container > *),\n:where(.ρfc > *) {\n  pointer-events: auto;\n}\n\n:where(.__wab_slot),\n:where(.ρs) {\n  display: contents;\n}\n\n:where(.__wab_slot-string-wrapper),\n:where(.ρsw) {\n  position: relative;\n}\n\n:where(.__wab_passthrough) {\n  display: contents;\n}\n\n:where(.__wab_img-wrapper) {\n  position: relative;\n  display: inherit;\n  flex-direction: column;\n  overflow: hidden;\n  flex-shrink: 0;\n}\n\n:where(.__wab_slot > .__wab_img-wrapper),\n:where(.ρs > .__wab_img-wrapper) {\n  display: block;\n}\n\n:where(.__wab_passthrough > .__wab_img-wrapper) {\n  display: block;\n}\n\n:where(.__wab_img-spacer-svg) {\n  display: block;\n  margin: 0;\n  border: none;\n  padding: 0;\n}\n\n:where(.__wab_img) {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  margin: 0;\n  padding: 0;\n  border: none;\n  display: block;\n  width: 100%;\n  min-width: 100%;\n  max-width: 100%;\n  min-height: 100%;\n  max-height: 100%;\n  box-sizing: border-box;\n}\n\n:where(.__wab_picture) {\n  display: contents;\n}\n");e("body {\n  background-color: #fff;\n  padding: 0;\n  margin: 0;\n  width: 100%;\n  min-height: 100vh;\n  height: 100vh;\n}\n\n#plasmic-app {\n  display: contents;\n}\n\n/*\n  Vertical centering trick copied from\n  https://steelkiwi.medium.com/vertical-centering-until-the-block-reaches-specified-height-ea2e985f461\n\n  Allows you to center vertically if content is shorter than screen height,\n  and then top-align otherwise. We only apply this for components, not for\n  pages though, because pages should always span the viewport height.\n*/\n.live-root-container {\n  width: 100%;\n  min-height: 100vh;\n  display: contents;\n}\n\n.live-root-container--centered {\n  display: flex;\n  justify-content: safe center;\n  align-items: safe center;\n}\n\n/*\n  We don't want to show error overlays whenever the host app is running\n  in dev mode and our error boundaries (e.g., the Loading Boundary) catch\n  an error.\n*/\nnextjs-portal {\n  display: none;\n}\n\nbody > iframe {\n  display: none;\n}\n\ngatsby-fast-refresh {\n  display: none;\n}\n");const n=window.SystemJS,t=window.__Sub;n.set(n.resolveSync("react"),n.newModule(t.React)),n.set(n.resolveSync("react-dom"),n.newModule(t.ReactDOM)),n.set(n.resolveSync("@plasmicapp/react-web"),n.newModule(window.__PlasmicReactWebBundle)),n.set(n.resolveSync("@plasmicapp/data-sources-context"),n.newModule(window.__PlasmicDataSourcesContextBundle)),n.set(n.resolveSync("@plasmicapp/host"),n.newModule(t)),t.PlasmicQuery&&(n.set(n.resolveSync("@plasmicapp/query"),n.newModule(t.PlasmicQuery)),n.set(n.resolveSync("@plasmicapp/react-web/lib/data-sources"),n.newModule(window.__PlasmicDataSourcesBundle))),n.refreshXModules([{name:"@plasmicapp/react-web/lib/plasmic.css",source:"/* nothing to see */",lang:"css"}])}();
//# sourceMappingURL=client.js.map
