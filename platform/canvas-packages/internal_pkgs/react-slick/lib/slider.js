"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true,
});
exports["default"] = void 0;

var _react = _interopRequireDefault(require("react"));

var _innerSlider = require("./inner-slider");

var _json2mq = _interopRequireDefault(require("json2mq"));

var _defaultProps = _interopRequireDefault(require("./default-props"));

var _innerSliderUtils = require("./utils/innerSliderUtils");

function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : { default: obj };
}

function _typeof(obj) {
  "@babel/helpers - typeof";
  if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
    _typeof = function _typeof(obj) {
      return typeof obj;
    };
  } else {
    _typeof = function _typeof(obj) {
      return obj &&
        typeof Symbol === "function" &&
        obj.constructor === Symbol &&
        obj !== Symbol.prototype
        ? "symbol"
        : typeof obj;
    };
  }
  return _typeof(obj);
}

function _extends() {
  _extends =
    Object.assign ||
    function (target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
  return _extends.apply(this, arguments);
}

function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    if (enumerableOnly)
      symbols = symbols.filter(function (sym) {
        return Object.getOwnPropertyDescriptor(object, sym).enumerable;
      });
    keys.push.apply(keys, symbols);
  }
  return keys;
}

function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};
    if (i % 2) {
      ownKeys(Object(source), true).forEach(function (key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(Object(source)).forEach(function (key) {
        Object.defineProperty(
          target,
          key,
          Object.getOwnPropertyDescriptor(source, key)
        );
      });
    }
  }
  return target;
}

function _classCallCheck(instance, Constructor) {
  if (!(instance instanceof Constructor)) {
    throw new TypeError("Cannot call a class as a function");
  }
}

function _defineProperties(target, props) {
  for (var i = 0; i < props.length; i++) {
    var descriptor = props[i];
    descriptor.enumerable = descriptor.enumerable || false;
    descriptor.configurable = true;
    if ("value" in descriptor) descriptor.writable = true;
    Object.defineProperty(target, descriptor.key, descriptor);
  }
}

function _createClass(Constructor, protoProps, staticProps) {
  if (protoProps) _defineProperties(Constructor.prototype, protoProps);
  if (staticProps) _defineProperties(Constructor, staticProps);
  return Constructor;
}

function _inherits(subClass, superClass) {
  if (typeof superClass !== "function" && superClass !== null) {
    throw new TypeError("Super expression must either be null or a function");
  }
  subClass.prototype = Object.create(superClass && superClass.prototype, {
    constructor: { value: subClass, writable: true, configurable: true },
  });
  if (superClass) _setPrototypeOf(subClass, superClass);
}

function _setPrototypeOf(o, p) {
  _setPrototypeOf =
    Object.setPrototypeOf ||
    function _setPrototypeOf(o, p) {
      o.__proto__ = p;
      return o;
    };
  return _setPrototypeOf(o, p);
}

function _createSuper(Derived) {
  var hasNativeReflectConstruct = _isNativeReflectConstruct();
  return function _createSuperInternal() {
    var Super = _getPrototypeOf(Derived),
      result;
    if (hasNativeReflectConstruct) {
      var NewTarget = _getPrototypeOf(this).constructor;
      result = Reflect.construct(Super, arguments, NewTarget);
    } else {
      result = Super.apply(this, arguments);
    }
    return _possibleConstructorReturn(this, result);
  };
}

function _possibleConstructorReturn(self, call) {
  if (call && (_typeof(call) === "object" || typeof call === "function")) {
    return call;
  }
  return _assertThisInitialized(self);
}

function _assertThisInitialized(self) {
  if (self === void 0) {
    throw new ReferenceError(
      "this hasn't been initialised - super() hasn't been called"
    );
  }
  return self;
}

function _isNativeReflectConstruct() {
  if (typeof Reflect === "undefined" || !Reflect.construct) return false;
  if (Reflect.construct.sham) return false;
  if (typeof Proxy === "function") return true;
  try {
    Date.prototype.toString.call(Reflect.construct(Date, [], function () {}));
    return true;
  } catch (e) {
    return false;
  }
}

function _getPrototypeOf(o) {
  _getPrototypeOf = Object.setPrototypeOf
    ? Object.getPrototypeOf
    : function _getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
      };
  return _getPrototypeOf(o);
}

function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value: value,
      enumerable: true,
      configurable: true,
      writable: true,
    });
  } else {
    obj[key] = value;
  }
  return obj;
}

var enquire = (0, _innerSliderUtils.canUseDOM)() && require("enquire.js");

var Slider = /*#__PURE__*/ (function (_React$Component) {
  _inherits(Slider, _React$Component);

  var _super = _createSuper(Slider);

  function Slider(props) {
    var _this;

    _classCallCheck(this, Slider);

    _this = _super.call(this, props);

    _defineProperty(
      _assertThisInitialized(_this),
      "innerSliderRefHandler",
      function (ref) {
        return (_this.innerSlider = ref);
      }
    );

    _defineProperty(_assertThisInitialized(_this), "slickPrev", function () {
      return _this.innerSlider.slickPrev();
    });

    _defineProperty(_assertThisInitialized(_this), "slickNext", function () {
      return _this.innerSlider.slickNext();
    });

    _defineProperty(
      _assertThisInitialized(_this),
      "slickGoTo",
      function (slide) {
        var dontAnimate =
          arguments.length > 1 && arguments[1] !== undefined
            ? arguments[1]
            : false;
        return _this.innerSlider.slickGoTo(slide, dontAnimate);
      }
    );

    _defineProperty(_assertThisInitialized(_this), "slickPause", function () {
      return _this.innerSlider.pause("paused");
    });

    _defineProperty(_assertThisInitialized(_this), "slickPlay", function () {
      return _this.innerSlider.autoPlay("play");
    });

    _this.state = {
      breakpoint: null,
    };
    _this._responsiveMediaHandlers = [];
    return _this;
  }

  _createClass(Slider, [
    {
      key: "media",
      value: function media(query, handler) {
        // javascript handler for  css media query
        enquire.register(query, handler);

        this._responsiveMediaHandlers.push({
          query: query,
          handler: handler,
        });
      }, // handles responsive breakpoints
    },
    {
      key: "componentDidMount",
      value: function componentDidMount() {
        var _this2 = this;

        // performance monitoring
        //if (process.env.NODE_ENV !== 'production') {
        //const { whyDidYouUpdate } = require('why-did-you-update')
        //whyDidYouUpdate(React)
        //}
        if (this.props.responsive) {
          var breakpoints = this.props.responsive.map(function (breakpt) {
            return breakpt.breakpoint;
          }); // sort them in increasing order of their numerical value

          breakpoints.sort(function (x, y) {
            return x - y;
          });
          breakpoints.forEach(function (breakpoint, index) {
            // media query for each breakpoint
            var bQuery;

            if (index === 0) {
              bQuery = (0, _json2mq["default"])({
                minWidth: 0,
                maxWidth: breakpoint,
              });
            } else {
              bQuery = (0, _json2mq["default"])({
                minWidth: breakpoints[index - 1] + 1,
                maxWidth: breakpoint,
              });
            } // when not using server side rendering

            (0, _innerSliderUtils.canUseDOM)() &&
              _this2.media(bQuery, function () {
                _this2.setState({
                  breakpoint: breakpoint,
                });
              });
          }); // Register media query for full screen. Need to support resize from small to large
          // convert javascript object to media query string

          var query = (0, _json2mq["default"])({
            minWidth: breakpoints.slice(-1)[0],
          });
          (0, _innerSliderUtils.canUseDOM)() &&
            this.media(query, function () {
              _this2.setState({
                breakpoint: null,
              });
            });
        }
      },
    },
    {
      key: "componentWillUnmount",
      value: function componentWillUnmount() {
        this._responsiveMediaHandlers.forEach(function (obj) {
          enquire.unregister(obj.query, obj.handler);
        });
      },
    },
    {
      key: "render",
      value: function render() {
        var _this3 = this;

        var settings;
        var newProps;

        if (this.state.breakpoint) {
          newProps = this.props.responsive.filter(function (resp) {
            return resp.breakpoint === _this3.state.breakpoint;
          });
          settings =
            newProps[0].settings === "unslick"
              ? "unslick"
              : _objectSpread(
                  _objectSpread(
                    _objectSpread({}, _defaultProps["default"]),
                    this.props
                  ),
                  newProps[0].settings
                );
        } else {
          settings = _objectSpread(
            _objectSpread({}, _defaultProps["default"]),
            this.props
          );
        } // force scrolling by one if centerMode is on

        if (settings.centerMode) {
          if (
            settings.slidesToScroll > 1 &&
            process.env.NODE_ENV !== "production"
          ) {
            console.warn(
              "slidesToScroll should be equal to 1 in centerMode, you are using ".concat(
                settings.slidesToScroll
              )
            );
          }

          settings.slidesToScroll = 1;
        } // force showing one slide and scrolling by one if the fade mode is on

        if (settings.fade) {
          if (
            settings.slidesToShow > 1 &&
            process.env.NODE_ENV !== "production"
          ) {
            console.warn(
              "slidesToShow should be equal to 1 when fade is true, you're using ".concat(
                settings.slidesToShow
              )
            );
          }

          if (
            settings.slidesToScroll > 1 &&
            process.env.NODE_ENV !== "production"
          ) {
            console.warn(
              "slidesToScroll should be equal to 1 when fade is true, you're using ".concat(
                settings.slidesToScroll
              )
            );
          }

          settings.slidesToShow = 1;
          settings.slidesToScroll = 1;
        } // makes sure that children is an array, even when there is only 1 child

        var children = _react["default"].Children.toArray(this.props.children); // Children may contain false or null, so we should filter them
        // children may also contain string filled with spaces (in certain cases where we use jsx strings)

        children = children.filter(function (child) {
          if (typeof child === "string") {
            return !!child.trim();
          }

          return !!child;
        }); // rows and slidesPerRow logic is handled here

        if (
          settings.variableWidth &&
          (settings.rows > 1 || settings.slidesPerRow > 1)
        ) {
          console.warn(
            "variableWidth is not supported in case of rows > 1 or slidesPerRow > 1"
          );
          settings.variableWidth = false;
        }

        var newChildren = [];
        var currentWidth = null;

        for (
          var i = 0;
          i < children.length;
          i += settings.rows * settings.slidesPerRow
        ) {
          var newSlide = [];

          for (
            var j = i;
            j < i + settings.rows * settings.slidesPerRow;
            j += settings.slidesPerRow
          ) {
            var row = [];

            for (var k = j; k < j + settings.slidesPerRow; k += 1) {
              if (settings.variableWidth && children[k].props.style) {
                currentWidth = children[k].props.style.width;
              }

              if (k >= children.length) break;
              row.push(
                /*#__PURE__*/ _react["default"].cloneElement(children[k], {
                  key: 100 * i + 10 * j + k,
                  tabIndex: -1,
                  style: {
                    width: "".concat(100 / settings.slidesPerRow, "%"),
                    display: "inline-block",
                  },
                })
              );
            }

            newSlide.push(
              /*#__PURE__*/ _react["default"].createElement(
                "div",
                {
                  key: 10 * i + j,
                },
                row
              )
            );
          }

          if (settings.variableWidth) {
            newChildren.push(
              /*#__PURE__*/ _react["default"].createElement(
                "div",
                {
                  key: i,
                  style: {
                    width: currentWidth,
                  },
                },
                newSlide
              )
            );
          } else {
            newChildren.push(
              /*#__PURE__*/ _react["default"].createElement(
                "div",
                {
                  key: i,
                },
                newSlide
              )
            );
          }
        }

        if (settings === "unslick") {
          var className = "regular slider " + (this.props.className || "");
          return /*#__PURE__*/ _react["default"].createElement(
            "div",
            {
              className: className,
            },
            children
          );
        } else if (newChildren.length <= settings.slidesToShow) {
          settings.unslick = true;
        }

        return /*#__PURE__*/ _react["default"].createElement(
          _innerSlider.InnerSlider,
          _extends(
            {
              style: this.props.style,
              ref: this.innerSliderRefHandler,
            },
            settings
          ),
          newChildren
        );
      },
    },
  ]);

  return Slider;
})(_react["default"].Component);

exports["default"] = Slider;
