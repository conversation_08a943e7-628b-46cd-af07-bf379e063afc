{"name": "canvas-packages", "scripts": {"build": "NODE_ENV=production node ./esbuild.js", "watch": "ESBUILD_WATCH=true node ./esbuild.js", "prepare": "node ./makePkgTypes.js", "size": "size-limit", "upgrade-aria": "yarn upgrade --latest react-aria && npx yarn-deduplicate@latest --scopes @react-aria @react-stately @react-types @internationalized && npx yarn-deduplicate@latest --packages react-aria @swc/helpers"}, "dependenciesComments": {"@ant-design/pro-components": "Must be 2.6.4. Earlier doesn't support newer antd, later incurs a disallowed dynamic require of antd/es/layout/Sider. https://app.shortcut.com/plasmic/story/37043/richlayout-always-initially-loads-with-dark-background", "rc-util": "Must be ^5.44.4. Earlier versions doesn't support React 19+ by attempting to use findDOMNode from react-dom. https://linear.app/plasmic/issue/PLA-11800/investigate-issue-with-rich-components-table-in-react-19"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@ant-design/pro-components": "2.6.4", "@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@faker-js/faker": "^8.2.0", "@plasmicapp/react-web": "^0.2.388", "@plasmicpkgs/airtable": "^0.0.232", "@plasmicpkgs/antd": "^2.0.140", "@plasmicpkgs/antd5": "^0.0.290", "@plasmicpkgs/commerce": "^0.0.216", "@plasmicpkgs/commerce-commercetools": "^0.0.165", "@plasmicpkgs/commerce-local": "^0.0.216", "@plasmicpkgs/commerce-saleor": "^0.0.180", "@plasmicpkgs/commerce-shopify": "^0.0.224", "@plasmicpkgs/commerce-swell": "^0.0.225", "@plasmicpkgs/fetch": "^0.0.8", "@plasmicpkgs/framer-motion": "^0.0.216", "@plasmicpkgs/lottie-react": "^0.0.209", "@plasmicpkgs/plasmic-basic-components": "^0.0.245", "@plasmicpkgs/plasmic-chakra-ui": "^0.0.48", "@plasmicpkgs/plasmic-cms": "^0.0.283", "@plasmicpkgs/plasmic-content-stack": "^0.0.172", "@plasmicpkgs/plasmic-contentful": "^0.0.160", "@plasmicpkgs/plasmic-embed-css": "^0.1.203", "@plasmicpkgs/plasmic-graphcms": "^0.0.189", "@plasmicpkgs/plasmic-link-preview": "^1.0.115", "@plasmicpkgs/plasmic-nav": "^0.0.188", "@plasmicpkgs/plasmic-query": "^0.0.237", "@plasmicpkgs/plasmic-rich-components": "^1.0.214", "@plasmicpkgs/plasmic-sanity-io": "^1.0.197", "@plasmicpkgs/plasmic-strapi": "^0.1.168", "@plasmicpkgs/plasmic-tabs": "^0.0.59", "@plasmicpkgs/plasmic-wordpress": "^0.0.139", "@plasmicpkgs/plasmic-wordpress-graphql": "^0.0.134", "@plasmicpkgs/radix-ui": "^0.0.76", "@plasmicpkgs/react-aria": "^0.0.140", "@plasmicpkgs/react-awesome-reveal": "^3.8.220", "@plasmicpkgs/react-chartjs-2": "^1.0.128", "@plasmicpkgs/react-parallax-tilt": "^0.0.218", "@plasmicpkgs/react-quill": "^1.0.81", "@plasmicpkgs/react-scroll-parallax": "^0.0.226", "@plasmicpkgs/react-slick": "^0.0.239", "@plasmicpkgs/react-twitter-widgets": "^0.0.216", "@plasmicpkgs/react-youtube": "^7.13.222", "@plasmicpkgs/rive": "^0.0.5", "@plasmicpkgs/tiptap": "^0.0.3", "@react-aria/focus": "^3.17.0", "@react-aria/interactions": "^3.21.2", "@react-aria/overlays": "^3.22.0", "@types/isomorphic-fetch": "^0.0.37", "@types/md5": "^2.3.3", "@types/papaparse": "^5.3.9", "@types/pluralize": "^0.0.31", "@types/semver": "^7.5.3", "@types/tinycolor2": "^1.4.4", "@types/uuid": "^9.0.5", "antd": "^5.12.7", "axios": "^1.5.1", "chart.js": "^4.2.1", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "date-fns": "^2.30.0", "dayjs": "^1.11.10", "fast-stringify": "^2.0.0", "framer-motion": "^7.6.1", "html-to-image": "^1.11.11", "immer": "^10.0.3", "internal-react-slick": "link:./internal_pkgs/react-slick", "isomorphic-fetch": "^3.0.0", "jquery": "^3.7.1", "lodash": "^4.17.21", "marked": "^9.1.1", "md5": "^2.3.0", "nanoid": "^5.0.2", "papaparse": "^5.4.1", "pluralize": "^8.0.0", "postcss": "^8.4.12", "random": "^4.1.0", "rc-util": "^5.44.4", "react-chartjs-2": "^5.2.0", "react-quill": "^2.0.0", "register-library": "file:../wab/src/wab/shared/register-library", "resize-observer-polyfill": "^1.5.1", "semver": "^7.5.4", "slate": "^0.81.1", "slate-react": "^0.81.0", "slick-carousel": "^1.8.1", "tinycolor2": "^1.6.0", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@babel/core": "^7.17.8", "@babel/preset-react": "^7.16.7", "@plasmicapp/host": "^1.0.218", "@rollup/plugin-alias": "^3.1.8", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^21.0.2", "@rollup/plugin-node-resolve": "^13.1.3", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-sucrase": "^4.0.2", "@rollup/plugin-typescript": "^8.3.1", "@size-limit/preset-app": "^11.1.4", "@types/jquery": "^3.5.22", "@types/react": "^17.0.43", "@types/react-dom": "^17.0.14", "@types/react-slick": "^0.23.8", "@yarnpkg/lockfile": "^1.1.0", "esbuild": "^0.15.11", "esbuild-plugin-alias": "^0.2.1", "esbuild-plugin-external-global": "^1.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^2.70.1", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "sha256": "^0.2.0", "size-limit": "^11.1.4", "typescript": "^4.7.4"}, "resolutions": {"@plasmicpkgs/antd/antd": "4.19.3", "@plasmicpkgs/react-slick/antd": "4.19.3", "rc-util": "^5.44.4"}, "size-limit": [{"path": "build/client.js", "limit": "60 KB"}]}