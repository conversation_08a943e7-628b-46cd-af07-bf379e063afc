import { strToU8 } from "fflate";
import { Middle<PERSON><PERSON><PERSON><PERSON> } from "hono";
import { HTTPException } from "hono/http-exception";
import { proxy } from "hono/proxy";
import crypto from "node:crypto";
import { handleBody } from "./compression";
import { Env } from "./env";
import { SaltManager } from "./salt";
import { uuidToString, uuidV7 } from "./uuid";
import { IpAddress, UNKNOWN_IP_ADDRESS, getIpAddress } from "./ip-address";

/**
 * Assume all properties could be missing.
 */
export interface PostHogEvent {
  uuid?: string;
  event?: string;
  properties?: PostHogProperties;
  offset?: number;
}

export interface PostHogProperties {
  /** PostHog public API token */
  token?: string;

  $raw_user_agent?: string;
  /** timestamp in seconds */
  $time?: number;
  $timezone?: string;
  $configured_session_timeout_ms?: number;
  distinct_id?: string;
  /**
   * Distinct ID before being identified. Sent with the $identify event.
   * https://posthog.com/docs/how-posthog-works/ingestion-pipeline
   * https://github.com/PostHog/posthog/blob/74b68f37a065d5e4389e7b78b5579e684353e5c2/plugin-server/functional_tests/analytics-ingestion/happy-path.test.ts#L579
   */
  $anon_distinct_id?: string;
  $device_id?: string;
  /** https://posthog.com/docs/data/sessions#custom-session-ids */
  $session_id?: string;
  $is_identified?: boolean;
  // ... other properties we don't care about
}

export const posthogHandler: MiddlewareHandler<Env> = async (ctx) => {
  const { POSTHOG_KEY, POSTHOG_API_HOST, POSTHOG_ASSETS_HOST } =
    ctx.get("processEnv");
  const ipAddress = getIpAddress(ctx.req.header("X-Forwarded-For"));

  // Extract the full path (includes search params)
  const url = new URL(ctx.req.url);

  // Determine which host to proxy to based on the path
  const posthogHost = url.pathname.startsWith("/static/")
    ? POSTHOG_ASSETS_HOST
    : POSTHOG_API_HOST;

  // Rewrite headers
  const headers: Record<string, string> = {
    ...ctx.req.header(),
    host: posthogHost, // add PostHog host
  };

  // Rewrite body (maybe)
  let body: RequestInit["body"] | null = ctx.req.raw.body;

  // Only for event ingestion endpoint
  if (url.pathname === "/i/v0/e/" || url.pathname === "/e/") {
    const saltManager = ctx.get("saltManager");
    body = await handleBody(ctx.req, (events: PostHogEvent | PostHogEvent[]) =>
      processEvents(events, POSTHOG_KEY, ipAddress, saltManager)
    );

    // Delete content-length since it might have changed
    delete headers["content-length"];
  }

  return proxy(`https://${posthogHost}${url.pathname}${url.search}`, {
    ...ctx.req, // forward all request data
    body,
    headers,
  });
};

/**
 * Processes events, injecting stable-ish user IDs to the request.
 * For unidentified events, overwrites distinct_id, $device_id, $session_id.
 * For $identity events, overwrite $anon_distinct_id.
 */
export async function processEvents(
  events: PostHogEvent | PostHogEvent[],
  expectedToken: string,
  ipAddress: string,
  saltManager: SaltManager
) {
  for (const event of Array.isArray(events) ? events : [events]) {
    if (event.properties?.token !== expectedToken) {
      throw new HTTPException(401, { message: "Invalid token" });
    }

    if (event.properties?.$is_identified === false) {
      const timestampMs = (event.properties.$time ?? Date.now() / 1000) * 1000;
      const timezone = event.properties.$timezone || "Etc/UTC";
      const userAgent =
        event.properties.$raw_user_agent || "unknown-user-agent";
      const sessionTimeoutMs =
        event.properties.$configured_session_timeout_ms || 1800000;

      const salt = await saltManager.getSalt(timestampMs, timezone);
      const userId = await generateUserId(salt, ipAddress, userAgent);
      const sessionId = await generateSessionId(
        userId,
        timestampMs,
        sessionTimeoutMs
      );

      event.properties.distinct_id = userId;
      event.properties.$device_id = userId;
      event.properties.$session_id = sessionId;
    } else if (
      event.event === "$identify" &&
      event.properties?.$anon_distinct_id
    ) {
      const timestampMs = (event.properties.$time ?? Date.now() / 1000) * 1000;
      const timezone = event.properties.$timezone || "Etc/UTC";
      const userAgent =
        event.properties.$raw_user_agent || "unknown-user-agent";

      const salt = await saltManager.getSalt(timestampMs, timezone);
      const userId = await generateUserId(salt, ipAddress, userAgent);

      event.properties.$anon_distinct_id = userId;
    }
  }
  return events;
}

/** Generates a user ID based on salt, IP address, and user agent. */
export async function generateUserId(
  salt: string,
  ipAddress: IpAddress,
  userAgent: string
): Promise<string> {
  if (ipAddress === UNKNOWN_IP_ADDRESS) {
    return "pcookieless_unknown"
  }

  const input = `${salt}:${ipAddress}:${userAgent}`;
  const digest = await crypto.subtle.digest("SHA-256", strToU8(input));
  return `pcookieless_${btoa(String.fromCharCode(...new Uint8Array(digest)))}`;
}

/**
 * Generates session ID based on user ID and session window.
 *
 * Since we can't actually store a session ID on the client, we approximate it
 * by choosing a session window based on the timestamp and session timeout.
 *
 * PostHog requires the session ID to be a UUID v7.
 */
export async function generateSessionId(
  userId: string,
  timestampMs: number,
  sessionTimeoutMs: number
): Promise<string> {
  // Choose the session window start by rounding down to the nearest session timeout interval
  const sessionStartMs =
    Math.floor(timestampMs / sessionTimeoutMs) * sessionTimeoutMs;

  // Combine userId and sessionStartMs before hashing
  const digest = await crypto.subtle.digest(
    "SHA-256",
    strToU8(`${userId}:${sessionTimeoutMs}:${sessionStartMs}`)
  );

  // Combine parts to form UUID v7
  return uuidToString(uuidV7(sessionStartMs, new Uint8Array(digest)));
}
