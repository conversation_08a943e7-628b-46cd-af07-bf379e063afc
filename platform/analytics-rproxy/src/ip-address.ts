export const UNKNOWN_IP_ADDRESS = Symbol("UNKNOWN_IP_ADDRESS");

export type IpAddress = string | typeof UNKNOWN_IP_ADDRESS;

/**
 * Gets IP address from X-Forwarded-For header, appended to by the load balancer.
 */
export function getIpAddress(xForwardedFor: string | undefined): IpAddress {
  if (!xForwardedFor) {
    return UNKNOWN_IP_ADDRESS;
  }

  // https://cloud.google.com/load-balancing/docs/https#x-forwarded-for_header
  const ips = xForwardedFor.split(",");
  if (ips.length < 2) {
    // LB should have appended 2 values. Don't trust if there's less than 2.
    return UNKNOWN_IP_ADDRESS;
  } else {
    // X-Forwarded-For: <existing-value>,<client-ip>,<load-balancer-ip>
    // Use the second to last value, since the <existing-value>
    // could contain anything, including other commas.
    return ips[ips.length - 2].trim() || UNKNOWN_IP_ADDRESS;
  }
}
