import { describe, expect, it } from "vitest";
import { getIpAddress, UNKNOWN_IP_ADDRESS } from "./ip-address";

describe("getIpAddress", () => {
  it("should return unknown if empty or undefined", () => {
    expect(getIpAddress(undefined)).toEqual(UNKNOWN_IP_ADDRESS);
    expect(getIpAddress("")).toEqual(UNKNOWN_IP_ADDRESS);
    expect(getIpAddress(",")).toEqual(UNKNOWN_IP_ADDRESS);
    expect(getIpAddress(",,")).toEqual(UNKNOWN_IP_ADDRESS);
    expect(getIpAddress(",missing-first")).toEqual(UNKNOWN_IP_ADDRESS);
  });
  it("should return unknown if there's only 1 value", () => {
    expect(getIpAddress("*******")).toEqual(UNKNOWN_IP_ADDRESS);
  });
  it("should take second to last value if there's 2 or more values", () => {
    expect(getIpAddress("*******,*******")).toEqual("*******");
    expect(getIpAddress(" ******* , ******* ")).toEqual("*******");
    expect(getIpAddress("*******, *******, **********")).toEqual("*******");
    expect(getIpAddress("*******,*******,**********,***********")).toEqual(
      "**********"
    );
  });
});
