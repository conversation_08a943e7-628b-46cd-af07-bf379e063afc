import { strFromU8, strToU8 } from "fflate";
import { KV as NatsKVStore } from "nats";
import { KV } from "./kv";

export class NatsKV implements KV {
  constructor(private readonly kv: NatsKVStore) {}

  async get(key: string): Promise<string | null> {
    const entry = await this.kv.get(key);
    return entry ? strFromU8(entry.value) : null;
  }

  async put(key: string, value: string): Promise<void> {
    await this.kv.put(key, strToU8(value));
  }
}
