// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import InsertPanel from "@/wab/client/components/insert-panel/InsertPanel";
import { useDismissibleStudioOverlay } from "@/wab/client/hooks/useDismissibleStudioOverlay";
import { useStudioCtx } from "@/wab/client/studio-ctx/StudioCtx";
import { spawnWrapper } from "@/wab/shared/common";
import { LeftTabKey } from "@/wab/shared/ui-config-utils";
import { observer } from "mobx-react";
import * as React from "react";
import { useEffect, useRef } from "react";
import { mergeProps, useOverlayPosition, useOverlayTrigger } from "react-aria";
import * as ReactDOM from "react-dom";
import { useOverlayTriggerState } from "react-stately";

const InsertPanelWrapper = observer(function InsertPanelWrapper_() {
  const savedLeftTabKey = useRef<LeftTabKey | undefined | null>(null);
  const studioCtx = useStudioCtx();
  const isOpen = studioCtx.showAddDrawer();

  useEffect(() => {
    if (isOpen) {
      savedLeftTabKey.current = studioCtx.leftTabKey;
      // studioCtx.leftTabKey = undefined;
    } else {
      if (savedLeftTabKey.current) {
        // studioCtx.leftTabKey = savedLeftTabKey.current;
        savedLeftTabKey.current = null;
      }
    }
  }, [isOpen]);

  const state = useOverlayTriggerState({
    isOpen,
    onOpenChange: spawnWrapper(async (isOpen_) => {
      return studioCtx.changeUnsafe(() => studioCtx.setShowAddDrawer(isOpen_));
    }),
  });

  const triggerRef = React.useRef<HTMLButtonElement>(null);
  const overlayRef = React.useRef<HTMLDivElement>(null);

  const { overlayProps: triggeredOverlayProps } = useOverlayTrigger(
    { type: "dialog" },
    state,
    triggerRef
  );

  const { overlayProps: overlayPositionProps } = useOverlayPosition({
    targetRef: triggerRef,
    overlayRef,
    placement: "bottom left",
    offset: 5,
    isOpen: state.isOpen,
  });

  useDismissibleStudioOverlay({
    overlayRef,
    isOpen,
    onDismiss: state.close,
  });

  return (
    <>
      {ReactDOM.createPortal(
        <div
          ref={overlayRef}
          {...mergeProps(triggeredOverlayProps, overlayPositionProps)}
          style={{
            display: state.isOpen ? "block" : "none",
            position: "absolute",
            left: 0,
            top: 48,
            zIndex: 1,
            width: "auto",
            height: "calc(100% - 48px)",
          }}
        >
          <InsertPanel onClose={state.close} />
        </div>,
        document.body
      )}
    </>
  );
});

export default InsertPanelWrapper;
