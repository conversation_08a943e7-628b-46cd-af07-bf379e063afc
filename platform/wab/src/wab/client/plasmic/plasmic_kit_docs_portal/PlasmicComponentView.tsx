// @ts-nocheck
/* eslint-disable */
/* tslint:disable */
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: dyzP6dbCdycwJpqiR2zkwe
// Component: fiIuU8gs9A
import * as React from "react";

import * as p from "@plasmicapp/react-web";

import {
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
} from "@plasmicapp/react-web";
import CodePreviewSnippet from "../../components/docs/CodePreviewSnippet"; // plasmic-import: X5avWz1hNF/component
import DocsPropsTable from "../../components/docs/DocsPropsTable"; // plasmic-import: 9vACp1cwGL/component
import DocsPropsTableRow from "../../components/docs/DocsPropsTableRow"; // plasmic-import: MQ5YoyUM0K/component
import TemplateRow from "../../components/docs/TemplateRow"; // plasmic-import: qFYdDOtl6B/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_design_system_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "./plasmic_plasmic_kit_docs_portal.module.css"; // plasmic-import: dyzP6dbCdycwJpqiR2zkwe/projectcss
import sty from "./PlasmicComponentView.module.css"; // plasmic-import: fiIuU8gs9A/css

export type PlasmicComponentView__VariantMembers = {
  componentType: "plume" | "code";
  hideCustomProps: "hideCustomProps";
};

export type PlasmicComponentView__VariantsArgs = {
  componentType?: SingleChoiceArg<"plume" | "code">;
  hideCustomProps?: SingleBooleanChoiceArg<"hideCustomProps">;
};

type VariantPropType = keyof PlasmicComponentView__VariantsArgs;
export const PlasmicComponentView__VariantProps = new Array<VariantPropType>(
  "componentType",
  "hideCustomProps"
);

export type PlasmicComponentView__ArgsType = {
  examples?: React.ReactNode;
  title?: React.ReactNode;
  info?: React.ReactNode;
  footer?: React.ReactNode;
};

type ArgPropType = keyof PlasmicComponentView__ArgsType;
export const PlasmicComponentView__ArgProps = new Array<ArgPropType>(
  "examples",
  "title",
  "info",
  "footer"
);

export type PlasmicComponentView__OverridesType = {
  root?: p.Flex<"div">;
  templatesList?: p.Flex<"div">;
  template?: p.Flex<"div">;
  viewport?: p.Flex<"div">;
  columns?: p.Flex<"div">;
  examples?: p.Flex<"div">;
  baseProps?: p.Flex<typeof DocsPropsTable>;
  customProps?: p.Flex<typeof DocsPropsTable>;
};

export interface DefaultComponentViewProps {
  examples?: React.ReactNode;
  title?: React.ReactNode;
  info?: React.ReactNode;
  footer?: React.ReactNode;
  componentType?: SingleChoiceArg<"plume" | "code">;
  hideCustomProps?: SingleBooleanChoiceArg<"hideCustomProps">;
  className?: string;
}

export const defaultComponentView__Args: Partial<PlasmicComponentView__ArgsType> =
  {};

function PlasmicComponentView__RenderFunc(props: {
  variants: PlasmicComponentView__VariantsArgs;
  args: PlasmicComponentView__ArgsType;
  overrides: PlasmicComponentView__OverridesType;

  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;
  const args = Object.assign({}, defaultComponentView__Args, props.args);
  const $props = args;

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootcomponentType_code]: hasVariant(
            variants,
            "componentType",
            "code"
          ),

          [sty.rootcomponentType_plume]: hasVariant(
            variants,
            "componentType",
            "plume"
          ),

          [sty.rootcomponentType_plume_hideCustomProps]:
            hasVariant(variants, "componentType", "plume") &&
            hasVariant(variants, "hideCustomProps", "hideCustomProps"),
        }
      )}
    >
      {(hasVariant(variants, "componentType", "code") ? true : true) ? (
        <div
          className={classNames(projectcss.all, sty.freeBox__wsP0Q, {
            [sty.freeBoxcomponentType_code__wsP0QLzWtc]: hasVariant(
              variants,
              "componentType",
              "code"
            ),
          })}
        >
          {(hasVariant(variants, "componentType", "code") ? true : false) ? (
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__ukrDh,
                {
                  [sty.textcomponentType_code__ukrDhLzWtc]: hasVariant(
                    variants,
                    "componentType",
                    "code"
                  ),
                }
              )}
            >
              {hasVariant(variants, "componentType", "code")
                ? "Templates"
                : "Base Props"}
            </div>
          ) : null}
          {(hasVariant(variants, "componentType", "code") ? true : true) ? (
            <div
              data-plasmic-name={"templatesList"}
              data-plasmic-override={overrides.templatesList}
              className={classNames(projectcss.all, sty.templatesList, {
                [sty.templatesListcomponentType_code]: hasVariant(
                  variants,
                  "componentType",
                  "code"
                ),
              })}
            >
              {(hasVariant(variants, "componentType", "code") ? true : true) ? (
                <TemplateRow
                  className={classNames(
                    "__wab_instance",
                    sty.templateRow__mzdT,
                    {
                      [sty.templateRowcomponentType_code__mzdTLzWtc]:
                        hasVariant(variants, "componentType", "code"),
                    }
                  )}
                  isActive={
                    hasVariant(variants, "componentType", "code")
                      ? true
                      : undefined
                  }
                >
                  <div
                    data-plasmic-name={"template"}
                    data-plasmic-override={overrides.template}
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.template,
                      {
                        [sty.templatecomponentType_code]: hasVariant(
                          variants,
                          "componentType",
                          "code"
                        ),
                      }
                    )}
                  >
                    {hasVariant(variants, "componentType", "code")
                      ? "Base"
                      : "Enter some text"}
                  </div>
                </TemplateRow>
              ) : null}
              {(
                hasVariant(variants, "componentType", "code") ? true : false
              ) ? (
                <TemplateRow
                  className={classNames(
                    "__wab_instance",
                    sty.templateRow__bte5,
                    {
                      [sty.templateRowcomponentType_code__bte5LzWtc]:
                        hasVariant(variants, "componentType", "code"),
                    }
                  )}
                >
                  {"Dark"}
                </TemplateRow>
              ) : null}
              {(
                hasVariant(variants, "componentType", "code") ? true : false
              ) ? (
                <TemplateRow
                  className={classNames(
                    "__wab_instance",
                    sty.templateRow___8Sspq,
                    {
                      [sty.templateRowcomponentType_code___8SspqLzWtc]:
                        hasVariant(variants, "componentType", "code"),
                    }
                  )}
                >
                  {"Disabled"}
                </TemplateRow>
              ) : null}
              {(
                hasVariant(variants, "componentType", "code") ? true : false
              ) ? (
                <TemplateRow
                  className={classNames(
                    "__wab_instance",
                    sty.templateRow___324Uz,
                    {
                      [sty.templateRowcomponentType_code___324UzLzWtc]:
                        hasVariant(variants, "componentType", "code"),
                    }
                  )}
                >
                  {"Horizontal"}
                </TemplateRow>
              ) : null}
            </div>
          ) : null}
        </div>
      ) : null}

      <div
        className={classNames(projectcss.all, sty.freeBox__vapgF, {
          [sty.freeBoxcomponentType_code__vapgFLzWtc]: hasVariant(
            variants,
            "componentType",
            "code"
          ),

          [sty.freeBoxcomponentType_plume__vapgFvhtOg]: hasVariant(
            variants,
            "componentType",
            "plume"
          ),

          [sty.freeBoxcomponentType_plume_hideCustomProps__vapgFvhtOgXQZyE]:
            hasVariant(variants, "componentType", "plume") &&
            hasVariant(variants, "hideCustomProps", "hideCustomProps"),
          [sty.freeBoxhideCustomProps__vapgFxQZyE]: hasVariant(
            variants,
            "hideCustomProps",
            "hideCustomProps"
          ),
        })}
      >
        <div className={classNames(projectcss.all, sty.freeBox__a1Db7)}>
          {p.renderPlasmicSlot({
            defaultContents: "MyCheckbox",
            value: args.title,
            className: classNames(sty.slotTargetTitle),
          })}
        </div>

        <div
          className={classNames(projectcss.all, sty.freeBox___1RuAb, {
            [sty.freeBoxcomponentType_code___1RuAbLzWtc]: hasVariant(
              variants,
              "componentType",
              "code"
            ),
          })}
        >
          {p.renderPlasmicSlot({
            defaultContents: "",
            value: args.info,
            className: classNames(sty.slotTargetInfo, {
              [sty.slotTargetInfocomponentType_code]: hasVariant(
                variants,
                "componentType",
                "code"
              ),

              [sty.slotTargetInfocomponentType_plume]: hasVariant(
                variants,
                "componentType",
                "plume"
              ),
            }),
          })}
        </div>

        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__fOsIs,
            {
              [sty.textcomponentType_code__fOsIsLzWtc]: hasVariant(
                variants,
                "componentType",
                "code"
              ),

              [sty.textcomponentType_plume__fOsIsvhtOg]: hasVariant(
                variants,
                "componentType",
                "plume"
              ),
            }
          )}
        >
          {hasVariant(variants, "componentType", "code")
            ? "Visualization"
            : "Examples of usage"}
        </div>

        {(hasVariant(variants, "componentType", "code") ? true : true) ? (
          <div
            data-plasmic-name={"viewport"}
            data-plasmic-override={overrides.viewport}
            className={classNames(projectcss.all, sty.viewport, {
              [sty.viewportcomponentType_code]: hasVariant(
                variants,
                "componentType",
                "code"
              ),
            })}
          />
        ) : null}
        {(hasVariant(variants, "componentType", "code") ? true : true) ? (
          <div
            data-plasmic-name={"columns"}
            data-plasmic-override={overrides.columns}
            className={classNames(projectcss.all, sty.columns, {
              [sty.columnscomponentType_code]: hasVariant(
                variants,
                "componentType",
                "code"
              ),
            })}
          >
            {(hasVariant(variants, "componentType", "code") ? true : true) ? (
              <div
                className={classNames(projectcss.all, sty.column__isLor, {
                  [sty.columncomponentType_code__isLorLzWtc]: hasVariant(
                    variants,
                    "componentType",
                    "code"
                  ),
                })}
              />
            ) : null}
            {(hasVariant(variants, "componentType", "code") ? true : true) ? (
              <div
                className={classNames(projectcss.all, sty.column__aoY9U, {
                  [sty.columncomponentType_code__aoY9ULzWtc]: hasVariant(
                    variants,
                    "componentType",
                    "code"
                  ),
                })}
              />
            ) : null}
          </div>
        ) : null}
        {(hasVariant(variants, "componentType", "code") ? true : true) ? (
          <p.Stack
            as={"div"}
            data-plasmic-name={"examples"}
            data-plasmic-override={overrides.examples}
            hasGap={true}
            className={classNames(projectcss.all, sty.examples, {
              [sty.examplescomponentType_code]: hasVariant(
                variants,
                "componentType",
                "code"
              ),

              [sty.examplescomponentType_plume]: hasVariant(
                variants,
                "componentType",
                "plume"
              ),
            })}
          >
            {p.renderPlasmicSlot({
              defaultContents: (
                <React.Fragment>
                  <CodePreviewSnippet
                    info={
                      <div
                        className={classNames(
                          projectcss.all,
                          projectcss.__wab_text,
                          sty.text__wfjsy
                        )}
                      >
                        {
                          "You can use isChecked/onChange to control your checkbox component."
                        }
                      </div>
                    }
                    title={
                      <div
                        className={classNames(
                          projectcss.all,
                          projectcss.__wab_text,
                          sty.text__fTmN9
                        )}
                      >
                        {"Controlled Checkbox"}
                      </div>
                    }
                  />

                  <CodePreviewSnippet
                    info={
                      "You can have an uncontrolled checkbox if you don't specify isChecked/onChange props."
                    }
                  />
                </React.Fragment>
              ),

              value: args.examples,
            })}
          </p.Stack>
        ) : null}

        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__s58E1,
            {
              [sty.textcomponentType_code__s58E1LzWtc]: hasVariant(
                variants,
                "componentType",
                "code"
              ),

              [sty.textcomponentType_plume__s58E1VhtOg]: hasVariant(
                variants,
                "componentType",
                "plume"
              ),
            }
          )}
        >
          {hasVariant(variants, "componentType", "code")
            ? "Props"
            : "Base Props"}
        </div>

        <DocsPropsTable
          data-plasmic-name={"baseProps"}
          data-plasmic-override={overrides.baseProps}
          className={classNames("__wab_instance", sty.baseProps, {
            [sty.basePropscomponentType_code]: hasVariant(
              variants,
              "componentType",
              "code"
            ),

            [sty.basePropscomponentType_plume]: hasVariant(
              variants,
              "componentType",
              "plume"
            ),
          })}
          rows={
            <React.Fragment>
              <DocsPropsTableRow
                className={classNames(
                  "__wab_instance",
                  sty.docsPropsTableRow__myc9J
                )}
                description={
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__gn2DO
                    )}
                  >
                    {"Controlled checkbox state."}
                  </div>
                }
                propName={"isChecked"}
              />

              <DocsPropsTableRow
                className={classNames(
                  "__wab_instance",
                  sty.docsPropsTableRow__jgJTo
                )}
                description={"Disable checkbox toggling."}
                propName={"isDisabled"}
              />

              <DocsPropsTableRow
                className={classNames(
                  "__wab_instance",
                  sty.docsPropsTableRow__t228J
                )}
                description={'Display checkbox in "indeterminate" state.'}
                propName={
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text___1FCzt
                    )}
                  >
                    {"isIndeterminate"}
                  </div>
                }
              />

              <DocsPropsTableRow
                className={classNames(
                  "__wab_instance",
                  sty.docsPropsTableRow___9FImz,
                  {
                    [sty.docsPropsTableRowcomponentType_code___9FImzLzWtc]:
                      hasVariant(variants, "componentType", "code"),
                  }
                )}
                description={'Value attribute for <input type="checkbox">'}
                propName={"value"}
                propType={
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__bMo5S
                    )}
                  >
                    {"string"}
                  </div>
                }
              />
            </React.Fragment>
          }
          rowsWithControl={
            <React.Fragment>
              {true ? (
                <DocsPropsTableRow
                  className={classNames(
                    "__wab_instance",
                    sty.docsPropsTableRow___7OuBz,
                    {
                      [sty.docsPropsTableRowcomponentType_code___7OuBzLzWtc]:
                        hasVariant(variants, "componentType", "code"),
                    }
                  )}
                  controlType={"boolean" as const}
                  description={"boolean state"}
                  propName={"boolean"}
                  propType={"boolean"}
                />
              ) : null}

              <DocsPropsTableRow
                className={classNames(
                  "__wab_instance",
                  sty.docsPropsTableRow___4Gv5U,
                  {
                    [sty.docsPropsTableRowcomponentType_code___4Gv5ULzWtc]:
                      hasVariant(variants, "componentType", "code"),
                  }
                )}
                controlType={"text" as const}
                description={"input text state"}
                propName={"string"}
              />

              <DocsPropsTableRow
                className={classNames(
                  "__wab_instance",
                  sty.docsPropsTableRow__kgZjM
                )}
                controlType={"choice" as const}
              />

              <DocsPropsTableRow
                className={classNames(
                  "__wab_instance",
                  sty.docsPropsTableRow__drijt,
                  {
                    [sty.docsPropsTableRowcomponentType_code__drijtLzWtc]:
                      hasVariant(variants, "componentType", "code"),
                  }
                )}
                controlType={"renderable" as const}
              />
            </React.Fragment>
          }
          showControls={
            hasVariant(variants, "componentType", "code") ? true : undefined
          }
        />

        {(
          hasVariant(variants, "hideCustomProps", "hideCustomProps")
            ? true
            : hasVariant(variants, "componentType", "code")
            ? true
            : true
        ) ? (
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__w9A9,
              {
                [sty.textcomponentType_code__w9A9LzWtc]: hasVariant(
                  variants,
                  "componentType",
                  "code"
                ),

                [sty.texthideCustomProps__w9A9XQZyE]: hasVariant(
                  variants,
                  "hideCustomProps",
                  "hideCustomProps"
                ),
              }
            )}
          >
            {"Custom Props"}
          </div>
        ) : null}
        {(
          hasVariant(variants, "hideCustomProps", "hideCustomProps")
            ? true
            : hasVariant(variants, "componentType", "code")
            ? true
            : true
        ) ? (
          <DocsPropsTable
            data-plasmic-name={"customProps"}
            data-plasmic-override={overrides.customProps}
            className={classNames("__wab_instance", sty.customProps, {
              [sty.customPropscomponentType_code]: hasVariant(
                variants,
                "componentType",
                "code"
              ),

              [sty.customPropshideCustomProps]: hasVariant(
                variants,
                "hideCustomProps",
                "hideCustomProps"
              ),
            })}
            rows={
              <React.Fragment>
                <DocsPropsTableRow
                  className={classNames(
                    "__wab_instance",
                    sty.docsPropsTableRow__ngOet
                  )}
                  description={"Custom variant."}
                  propName={"color"}
                  propType={'"blue" | "red"'}
                />

                <DocsPropsTableRow
                  className={classNames(
                    "__wab_instance",
                    sty.docsPropsTableRow__bsjqs
                  )}
                  description={"Custom slot."}
                  propName={
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__zpDRm
                      )}
                    >
                      {"customSlot"}
                    </div>
                  }
                  propType={"ReactNode"}
                />
              </React.Fragment>
            }
            rowsWithControl={
              <React.Fragment>
                {true ? (
                  <DocsPropsTableRow
                    className={classNames(
                      "__wab_instance",
                      sty.docsPropsTableRow__g7Gtw
                    )}
                    controlType={"boolean" as const}
                    description={"boolean state"}
                    propName={"boolean"}
                    propType={"boolean"}
                  />
                ) : null}

                <DocsPropsTableRow
                  className={classNames(
                    "__wab_instance",
                    sty.docsPropsTableRow__vYLs
                  )}
                  controlType={"text" as const}
                  description={"input text state"}
                  propName={"string"}
                />

                <DocsPropsTableRow
                  className={classNames(
                    "__wab_instance",
                    sty.docsPropsTableRow__sY5Lb
                  )}
                  controlType={"choice" as const}
                />

                <DocsPropsTableRow
                  className={classNames(
                    "__wab_instance",
                    sty.docsPropsTableRow__b2Oap
                  )}
                  controlType={"renderable" as const}
                />
              </React.Fragment>
            }
          />
        ) : null}
        {(hasVariant(variants, "componentType", "code") ? true : true) ? (
          <div
            className={classNames(projectcss.all, sty.freeBox__z6TZh, {
              [sty.freeBoxcomponentType_code__z6TZhLzWtc]: hasVariant(
                variants,
                "componentType",
                "code"
              ),
            })}
          >
            {p.renderPlasmicSlot({
              defaultContents: "",
              value: args.footer,
              className: classNames(sty.slotTargetFooter, {
                [sty.slotTargetFootercomponentType_code]: hasVariant(
                  variants,
                  "componentType",
                  "code"
                ),

                [sty.slotTargetFootercomponentType_plume]: hasVariant(
                  variants,
                  "componentType",
                  "plume"
                ),
              }),
            })}
          </div>
        ) : null}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "templatesList",
    "template",
    "viewport",
    "columns",
    "examples",
    "baseProps",
    "customProps",
  ],

  templatesList: ["templatesList", "template"],
  template: ["template"],
  viewport: ["viewport"],
  columns: ["columns"],
  examples: ["examples"],
  baseProps: ["baseProps"],
  customProps: ["customProps"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  templatesList: "div";
  template: "div";
  viewport: "div";
  columns: "div";
  examples: "div";
  baseProps: typeof DocsPropsTable;
  customProps: typeof DocsPropsTable;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicComponentView__OverridesType,
  DescendantsType<T>
>;

type NodeComponentProps<T extends NodeNameType> = {
  // Explicitly specify variants, args, and overrides as objects
  variants?: PlasmicComponentView__VariantsArgs;
  args?: PlasmicComponentView__ArgsType;
  overrides?: NodeOverridesType<T>;
} & Omit<PlasmicComponentView__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
  // Specify args directly as props
  Omit<PlasmicComponentView__ArgsType, ReservedPropsType> &
  // Specify overrides for each element directly as props
  Omit<
    NodeOverridesType<T>,
    ReservedPropsType | VariantPropType | ArgPropType
  > &
  // Specify props for the root element
  Omit<
    Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
    ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
  >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = deriveRenderOpts(props, {
      name: nodeName,
      descendantNames: [...PlasmicDescendants[nodeName]],
      internalArgPropNames: PlasmicComponentView__ArgProps,
      internalVariantPropNames: PlasmicComponentView__VariantProps,
    });

    return PlasmicComponentView__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicComponentView";
  } else {
    func.displayName = `PlasmicComponentView.${nodeName}`;
  }
  return func;
}

export const PlasmicComponentView = Object.assign(
  // Top-level PlasmicComponentView renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    templatesList: makeNodeComponent("templatesList"),
    template: makeNodeComponent("template"),
    viewport: makeNodeComponent("viewport"),
    columns: makeNodeComponent("columns"),
    examples: makeNodeComponent("examples"),
    baseProps: makeNodeComponent("baseProps"),
    customProps: makeNodeComponent("customProps"),

    // Metadata about props expected for PlasmicComponentView
    internalVariantProps: PlasmicComponentView__VariantProps,
    internalArgProps: PlasmicComponentView__ArgProps,
  }
);

export default PlasmicComponentView;
/* prettier-ignore-end */
