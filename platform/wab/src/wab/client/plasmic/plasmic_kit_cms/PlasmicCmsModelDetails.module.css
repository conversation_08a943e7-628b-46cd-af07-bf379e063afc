.root {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  max-width: 100%;
  background: var(--token-iR8SeEwQZ);
  min-width: 0;
  min-height: 0;
}
.freeBox___2Vfpj {
  display: flex;
  position: relative;
  justify-content: space-between;
  padding: 1.25rem;
  border-bottom: 1px solid var(--token-hoA5qaM-91G);
}
.left {
  display: flex;
  position: relative;
  flex-direction: column;
}
.modelName:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.modelNameValue {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  color: var(--token-0IloF6TmFvF);
  font-size: 14px;
  font-weight: 500;
  min-width: 0;
}
.svg__uGeJ {
  position: relative;
  object-fit: cover;
  left: auto;
  top: auto;
  color: var(--token-UunsGa2Y3t3);
  height: 1em;
}
.text {
  position: relative;
  color: var(--token-UunsGa2Y3t3);
}
.right {
  display: flex;
  position: relative;
  flex-direction: row;
}
.right > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.right > :global(.__wab_flex-container) > *,
.right > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.right > :global(.__wab_flex-container) > picture > img,
.right > :global(.__wab_flex-container) > :global(.__wab_slot) > picture > img {
  margin-left: 8px;
}
.saveButton:global(.__wab_instance) {
  position: relative;
}
.svg__cm2N1 {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.svg__xu6Os {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.menuButton:global(.__wab_instance):global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.freeBox__qDko6 {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  max-width: 100%;
  background: var(--token-p-rw5DRJTx);
  overflow: auto;
  min-width: 0;
  min-height: 0;
}
.freeBox__lCBih {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  max-width: 800px;
  min-width: 0;
  min-height: 0;
  padding: 1.25rem;
}
