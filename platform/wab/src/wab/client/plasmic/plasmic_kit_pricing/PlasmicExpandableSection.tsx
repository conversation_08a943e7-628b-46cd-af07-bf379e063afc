// @ts-nocheck
/* eslint-disable */
/* tslint:disable */
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: ehckhYnyDHgCBbV47m9bkf
// Component: IzGvUfmCzHyO

import * as React from "react";

import {
  get as $stateGet,
  set as $stateSet,
  Flex as Flex__,
  PlasmicIcon as PlasmicIcon__,
  SingleBooleanChoiceArg,
  Stack as Stack__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  hasVariant,
  renderPlasmicSlot,
  useCurrentUser,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import { useEnvironment } from "./PlasmicGlobalVariant__Environment"; // plasmic-import: hIjF9NLAUKG-/globalVariant

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "./plasmic_plasmic_kit_pricing.module.css"; // plasmic-import: ehckhYnyDHgCBbV47m9bkf/projectcss
import sty from "./PlasmicExpandableSection.module.css"; // plasmic-import: IzGvUfmCzHyO/css

import ChevronDownsvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import ChevronUpsvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronUpSvg"; // plasmic-import: i9D87DzsX/icon

createPlasmicElementProxy;

export type PlasmicExpandableSection__VariantMembers = {
  open: "open";
  dark: "dark";
};
export type PlasmicExpandableSection__VariantsArgs = {
  open?: SingleBooleanChoiceArg<"open">;
  dark?: SingleBooleanChoiceArg<"dark">;
};
type VariantPropType = keyof PlasmicExpandableSection__VariantsArgs;
export const PlasmicExpandableSection__VariantProps =
  new Array<VariantPropType>("open", "dark");

export type PlasmicExpandableSection__ArgsType = {
  title?: React.ReactNode;
  body?: React.ReactNode;
};
type ArgPropType = keyof PlasmicExpandableSection__ArgsType;
export const PlasmicExpandableSection__ArgProps = new Array<ArgPropType>(
  "title",
  "body"
);

export type PlasmicExpandableSection__OverridesType = {
  root?: Flex__<"div">;
  header?: Flex__<"div">;
  svg?: Flex__<"svg">;
  content?: Flex__<"div">;
};

export interface DefaultExpandableSectionProps {
  title?: React.ReactNode;
  body?: React.ReactNode;
  open?: SingleBooleanChoiceArg<"open">;
  dark?: SingleBooleanChoiceArg<"dark">;
  className?: string;
}

const $$ = {};

function PlasmicExpandableSection__RenderFunc(props: {
  variants: PlasmicExpandableSection__VariantsArgs;
  args: PlasmicExpandableSection__ArgsType;
  overrides: PlasmicExpandableSection__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(() => Object.assign({}, props.args), [props.args]);

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const currentUser = useCurrentUser?.() || {};

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "open",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.open,
      },
      {
        path: "dark",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.dark,
      },
    ],

    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  const globalVariants = ensureGlobalVariants({
    environment: useEnvironment(),
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [projectcss.global_environment_website]: hasVariant(
            globalVariants,
            "environment",
            "website"
          ),
          [sty.rootdark]: hasVariant($state, "dark", "dark"),
          [sty.rootopen]: hasVariant($state, "open", "open"),
        }
      )}
    >
      <Stack__
        as={"div"}
        data-plasmic-name={"header"}
        data-plasmic-override={overrides.header}
        hasGap={true}
        className={classNames(projectcss.all, sty.header, {
          [sty.headerdark]: hasVariant($state, "dark", "dark"),
          [sty.headeropen]: hasVariant($state, "open", "open"),
        })}
        onClick={async (event) => {
          const $steps = {};

          $steps["updateOpen"] = true
            ? (() => {
                const actionArgs = { vgroup: "open", operation: 2 };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  const oldValue = $stateGet($state, vgroup);
                  $stateSet($state, vgroup, !oldValue);
                  return !oldValue;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateOpen"] != null &&
            typeof $steps["updateOpen"] === "object" &&
            typeof $steps["updateOpen"].then === "function"
          ) {
            $steps["updateOpen"] = await $steps["updateOpen"];
          }
        }}
      >
        <div
          className={classNames(projectcss.all, sty.freeBox__i5DdO, {
            [sty.freeBoxdark__i5DdOzRgRj]: hasVariant($state, "dark", "dark"),
            [sty.freeBoxopen__i5DdOnflii]: hasVariant($state, "open", "open"),
          })}
        >
          <div
            className={classNames(projectcss.all, sty.freeBox__eNnlX, {
              [sty.freeBoxopen__eNnlXnflii]: hasVariant($state, "open", "open"),
            })}
          >
            <div
              className={classNames(projectcss.all, sty.freeBox__x0N39, {
                [sty.freeBoxopen__x0N39Nflii]: hasVariant(
                  $state,
                  "open",
                  "open"
                ),
              })}
            >
              {renderPlasmicSlot({
                defaultContents: "All features",
                value: args.title,
                className: classNames(sty.slotTargetTitle, {
                  [sty.slotTargetTitledark]: hasVariant($state, "dark", "dark"),
                  [sty.slotTargetTitleopen]: hasVariant($state, "open", "open"),
                }),
              })}
            </div>
          </div>
          <PlasmicIcon__
            data-plasmic-name={"svg"}
            data-plasmic-override={overrides.svg}
            PlasmicIconType={
              hasVariant($state, "open", "open")
                ? ChevronUpsvgIcon
                : ChevronDownsvgIcon
            }
            className={classNames(projectcss.all, sty.svg, {
              [sty.svgdark]: hasVariant($state, "dark", "dark"),
              [sty.svgopen]: hasVariant($state, "open", "open"),
            })}
            role={"img"}
          />
        </div>
      </Stack__>
      {(hasVariant($state, "open", "open") ? true : false) ? (
        <div
          data-plasmic-name={"content"}
          data-plasmic-override={overrides.content}
          className={classNames(projectcss.all, sty.content, {
            [sty.contentdark]: hasVariant($state, "dark", "dark"),
            [sty.contentopen]: hasVariant($state, "open", "open"),
          })}
        >
          {renderPlasmicSlot({
            defaultContents: (
              <div className={classNames(projectcss.all, sty.freeBox__k1Ze5)}>
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___8C9RY
                  )}
                >
                  {
                    "A workspace is an organizational unit within an organization. Organizations can contain multiple workspaces, and each workspace can house numerous Plasmic projects. Organization members can be added to workspaces or individual projects. For example, you could have an organization for your agency with separate workspaces for each client, or an organization for your company with workspaces for each department."
                  }
                </div>
              </div>
            ),

            value: args.body,
            className: classNames(sty.slotTargetBody, {
              [sty.slotTargetBodydark]: hasVariant($state, "dark", "dark"),
              [sty.slotTargetBodyopen]: hasVariant($state, "open", "open"),
            }),
          })}
        </div>
      ) : null}
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "header", "svg", "content"],
  header: ["header", "svg"],
  svg: ["svg"],
  content: ["content"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  header: "div";
  svg: "svg";
  content: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicExpandableSection__OverridesType,
  DescendantsType<T>
>;

type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicExpandableSection__VariantsArgs;
    args?: PlasmicExpandableSection__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicExpandableSection__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicExpandableSection__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicExpandableSection__ArgProps,
          internalVariantPropNames: PlasmicExpandableSection__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicExpandableSection__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicExpandableSection";
  } else {
    func.displayName = `PlasmicExpandableSection.${nodeName}`;
  }
  return func;
}

export const PlasmicExpandableSection = Object.assign(
  // Top-level PlasmicExpandableSection renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    header: makeNodeComponent("header"),
    svg: makeNodeComponent("svg"),
    content: makeNodeComponent("content"),

    // Metadata about props expected for PlasmicExpandableSection
    internalVariantProps: PlasmicExpandableSection__VariantProps,
    internalArgProps: PlasmicExpandableSection__ArgProps,
  }
);

export default PlasmicExpandableSection;
/* prettier-ignore-end */
