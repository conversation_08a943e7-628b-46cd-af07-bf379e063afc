// @ts-nocheck
/* eslint-disable */
/* tslint:disable */
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: 2dMe7XWUq916KsPnra5vYj
// Component: HQf5xQMdD4

import * as React from "react";

import * as p from "@plasmicapp/react-web";
import * as ph from "@plasmicapp/react-web/lib/host";

import {
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import IconButton from "../../components/widgets/IconButton"; // plasmic-import: LPry-TF4j22a/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import plasmic_plasmic_kit_design_system_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "./plasmic_plasmic_kit_end_user_management.module.css"; // plasmic-import: 2dMe7XWUq916KsPnra5vYj/projectcss
import sty from "./PlasmicAuthConfigToken.module.css"; // plasmic-import: HQf5xQMdD4/css

import CopysvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__CopySvg"; // plasmic-import: aGIZL6Ec9/icon

createPlasmicElementProxy;

export type PlasmicAuthConfigToken__VariantMembers = {};
export type PlasmicAuthConfigToken__VariantsArgs = {};
type VariantPropType = keyof PlasmicAuthConfigToken__VariantsArgs;
export const PlasmicAuthConfigToken__VariantProps =
  new Array<VariantPropType>();

export type PlasmicAuthConfigToken__ArgsType = {
  token?: React.ReactNode;
};
type ArgPropType = keyof PlasmicAuthConfigToken__ArgsType;
export const PlasmicAuthConfigToken__ArgProps = new Array<ArgPropType>("token");

export type PlasmicAuthConfigToken__OverridesType = {
  root?: p.Flex<"div">;
  tokenLabel?: p.Flex<"div">;
  copyBtn?: p.Flex<typeof IconButton>;
  svg?: p.Flex<"svg">;
};

export interface DefaultAuthConfigTokenProps {
  token?: React.ReactNode;
  className?: string;
}

const __wrapUserFunction =
  globalThis.__PlasmicWrapUserFunction ?? ((loc, fn) => fn());
const __wrapUserPromise =
  globalThis.__PlasmicWrapUserPromise ??
  (async (loc, promise) => {
    return await promise;
  });

function PlasmicAuthConfigToken__RenderFunc(props: {
  variants: PlasmicAuthConfigToken__VariantsArgs;
  args: PlasmicAuthConfigToken__ArgsType;
  overrides: PlasmicAuthConfigToken__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(() => Object.assign({}, props.args), [props.args]);

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = ph.useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const currentUser = p.useCurrentUser?.() || {};

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_style_controls_css.plasmic_tokens,
        sty.root
      )}
    >
      <div className={classNames(projectcss.all, sty.column__mUq5E)}>
        <div
          data-plasmic-name={"tokenLabel"}
          data-plasmic-override={overrides.tokenLabel}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.tokenLabel
          )}
        >
          {"Secret token"}
        </div>
      </div>
      <div className={classNames(projectcss.all, sty.column__vsg2R)}>
        {true ? (
          <p.Stack
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__gmOhR)}
          >
            <div className={classNames(projectcss.all, sty.freeBox__tQh1)}>
              <div className={classNames(projectcss.all, sty.freeBox__aFmJ)}>
                {p.renderPlasmicSlot({
                  defaultContents:
                    "N5JFY3Hos63V1RuljzcNEESK8EPT6YfDTCwFeqk0rGCz2XfgTSPnznvuqnqRcbpnk9204ySst88m3IatsXD1iA",
                  value: args.token,
                  className: classNames(sty.slotTargetToken),
                })}
              </div>
            </div>
            <IconButton
              data-plasmic-name={"copyBtn"}
              data-plasmic-override={overrides.copyBtn}
              className={classNames("__wab_instance", sty.copyBtn)}
            >
              <CopysvgIcon
                data-plasmic-name={"svg"}
                data-plasmic-override={overrides.svg}
                className={classNames(projectcss.all, sty.svg)}
                role={"img"}
              />
            </IconButton>
          </p.Stack>
        ) : null}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "tokenLabel", "copyBtn", "svg"],
  tokenLabel: ["tokenLabel"],
  copyBtn: ["copyBtn", "svg"],
  svg: ["svg"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  tokenLabel: "div";
  copyBtn: typeof IconButton;
  svg: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicAuthConfigToken__OverridesType,
  DescendantsType<T>
>;

type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicAuthConfigToken__VariantsArgs;
    args?: PlasmicAuthConfigToken__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicAuthConfigToken__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicAuthConfigToken__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: [...PlasmicDescendants[nodeName]],
          internalArgPropNames: PlasmicAuthConfigToken__ArgProps,
          internalVariantPropNames: PlasmicAuthConfigToken__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicAuthConfigToken__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicAuthConfigToken";
  } else {
    func.displayName = `PlasmicAuthConfigToken.${nodeName}`;
  }
  return func;
}

export const PlasmicAuthConfigToken = Object.assign(
  // Top-level PlasmicAuthConfigToken renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    tokenLabel: makeNodeComponent("tokenLabel"),
    copyBtn: makeNodeComponent("copyBtn"),
    svg: makeNodeComponent("svg"),

    // Metadata about props expected for PlasmicAuthConfigToken
    internalVariantProps: PlasmicAuthConfigToken__VariantProps,
    internalArgProps: PlasmicAuthConfigToken__ArgProps,
  }
);

export default PlasmicAuthConfigToken;
/* prettier-ignore-end */
