.root {
  display: flex;
  flex-direction: column;
  width: 300px;
  height: auto;
  max-width: 100%;
  position: relative;
  background: #ffffff;
}
.root > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  margin-top: calc(0px - 8px);
  height: calc(100% + 8px);
}
.root > :global(.__wab_flex-container) > *,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.root > :global(.__wab_flex-container) > picture > img,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > picture > img {
  margin-top: 8px;
}
.freeBox__njtN0 {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.freeBoxisPreviewing__njtN0ZvkP {
  max-height: 300px;
  overflow: auto;
}
.bodyInputisPreviewing:global(.__wab_instance):global(.__wab_instance) {
  display: none;
}
.svg___4C5CB {
  position: relative;
  height: 1em;
}
.svg__pl1No {
  position: relative;
  height: 1em;
}
.body {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  display: none;
  padding: var(--token-uzWT6AFCY);
}
.bodyisPreviewing {
  display: block;
}
.freeBox__ekXz {
  display: flex;
  flex-direction: row;
  position: relative;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.freeBoxisEditing__ekXzZsQtK {
  justify-content: flex-end;
  align-items: center;
}
.freeBox__rndZx {
  display: flex;
  flex-direction: row;
  position: relative;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.mentionIcon:global(.__wab_instance) {
  max-width: 100%;
}
.svg__wnixe {
  object-fit: cover;
  max-width: 100%;
  color: #60646c;
  height: 1em;
}
.svg__aVLgH {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.shareProjectIcon:global(.__wab_instance) {
  max-width: 100%;
}
.shareProjectIconisEditing:global(.__wab_instance) {
  display: none;
}
.svg__t3Ip6 {
  object-fit: cover;
  max-width: 100%;
  color: #60646c;
  height: 1em;
}
.svg___5OJwv {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.markdownHintsIcon:global(.__wab_instance) {
  max-width: 100%;
}
.markdownHintsIconisEditing:global(.__wab_instance) {
  display: none;
}
.svg__kDvAo {
  object-fit: cover;
  max-width: 100%;
  color: #60646c;
  height: 1em;
}
.svg__zdVz0 {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.freeBox__jbqOp {
  display: flex;
  flex-direction: row;
  position: relative;
}
.freeBox__jbqOp > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  margin-left: calc(0px - var(--token-uzWT6AFCY));
  width: calc(100% + var(--token-uzWT6AFCY));
}
.freeBox__jbqOp > :global(.__wab_flex-container) > *,
.freeBox__jbqOp > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__jbqOp > :global(.__wab_flex-container) > picture > img,
.freeBox__jbqOp
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-uzWT6AFCY);
}
.previewButton:global(.__wab_instance):global(.__wab_instance) {
  max-width: 100%;
}
.svg__eZVtr {
  position: relative;
  height: 1em;
}
.text__geIt {
  white-space: pre;
  padding-right: 0px;
}
.svg___0LNc {
  position: relative;
  height: 1em;
}
.cancelButton:global(.__wab_instance):global(.__wab_instance) {
  max-width: 100%;
  display: none;
}
.cancelButtonisEditing:global(.__wab_instance):global(.__wab_instance) {
  display: flex;
}
.svg__ozbe6 {
  position: relative;
  height: 1em;
}
.text___9U10X {
  white-space: pre;
}
.svg__siuFv {
  position: relative;
  height: 1em;
}
.svg__wdMwt {
  position: relative;
  height: 1em;
}
.text__zljgi {
  white-space: pre;
}
.svg__kNjcE {
  position: relative;
  height: 1em;
}
