// @ts-nocheck
/* eslint-disable */
/* tslint:disable */
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: m8VxGcigeLAEXFe8c12w5Q
// Component: iWeSjEMdI3

import * as React from "react";

import {
  Flex as Flex__,
  PlasmicIcon as PlasmicIcon__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import ListItem from "../../components/ListItem"; // plasmic-import: v31d9_ANqk/component
import IconButton from "../../components/widgets/IconButton"; // plasmic-import: LPry-TF4j22a/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "./plasmic_project_panel.module.css"; // plasmic-import: m8VxGcigeLAEXFe8c12w5Q/projectcss
import sty from "./PlasmicFolderItem.module.css"; // plasmic-import: iWeSjEMdI3/css

import GearIcon from "../plasmic_kit/PlasmicIcon__Gear"; // plasmic-import: ZmVZmXEc9f_SR/icon
import FolderIcon from "../plasmic_kit_design_system/icons/PlasmicIcon__Folder"; // plasmic-import: hRo7v6cqW6/icon
import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import ChevronRightSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronRightSvg"; // plasmic-import: HBGx-zeiX/icon
import ComponentSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ComponentSvg"; // plasmic-import: vJVrKlrDD/icon
import File2SvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__File2Svg"; // plasmic-import: zldfLXBdc/icon
import FolderSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__FolderSvg"; // plasmic-import: zvkxMkUIX/icon
import GitBranchSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__GitBranchSvg"; // plasmic-import: 4OBJfCUZH/icon
import GridMasonrySvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__GridMasonrySvg"; // plasmic-import: f5dXpZvP5/icon

createPlasmicElementProxy;

export type PlasmicFolderItem__VariantMembers = {
  type:
    | "page"
    | "component"
    | "arena"
    | "folderOpen"
    | "folderClosed"
    | "branch";
  selected: "selected";
  nested: "nested";
};
export type PlasmicFolderItem__VariantsArgs = {
  type?: SingleChoiceArg<
    "page" | "component" | "arena" | "folderOpen" | "folderClosed" | "branch"
  >;

  selected?: SingleBooleanChoiceArg<"selected">;
  nested?: SingleBooleanChoiceArg<"nested">;
};
type VariantPropType = keyof PlasmicFolderItem__VariantsArgs;
export const PlasmicFolderItem__VariantProps = new Array<VariantPropType>(
  "type",
  "selected",
  "nested"
);

export type PlasmicFolderItem__ArgsType = {
  children?: React.ReactNode;
};
type ArgPropType = keyof PlasmicFolderItem__ArgsType;
export const PlasmicFolderItem__ArgProps = new Array<ArgPropType>("children");

export type PlasmicFolderItem__OverridesType = {
  root?: Flex__<"div">;
  listItem?: Flex__<typeof ListItem>;
  iconButton?: Flex__<typeof IconButton>;
};

export interface DefaultFolderItemProps {
  children?: React.ReactNode;
  type?: SingleChoiceArg<
    "page" | "component" | "arena" | "folderOpen" | "folderClosed" | "branch"
  >;

  selected?: SingleBooleanChoiceArg<"selected">;
  nested?: SingleBooleanChoiceArg<"nested">;
  className?: string;
}

const $$ = {};

function PlasmicFolderItem__RenderFunc(props: {
  variants: PlasmicFolderItem__VariantsArgs;
  args: PlasmicFolderItem__ArgsType;
  overrides: PlasmicFolderItem__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "type",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.type,
      },
      {
        path: "selected",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.selected,
      },
      {
        path: "nested",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.nested,
      },
    ],

    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootnested]: hasVariant($state, "nested", "nested"),
          [sty.rootselected]: hasVariant($state, "selected", "selected"),
          [sty.roottype_arena]: hasVariant($state, "type", "arena"),
          [sty.roottype_component]: hasVariant($state, "type", "component"),
          [sty.roottype_folderClosed]: hasVariant(
            $state,
            "type",
            "folderClosed"
          ),
          [sty.roottype_folderOpen]: hasVariant($state, "type", "folderOpen"),
          [sty.roottype_page]: hasVariant($state, "type", "page"),
        }
      )}
    >
      <ListItem
        data-plasmic-name={"listItem"}
        data-plasmic-override={overrides.listItem}
        actions={
          <IconButton
            data-plasmic-name={"iconButton"}
            data-plasmic-override={overrides.iconButton}
            children2={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__i2ZUu)}
                role={"img"}
              />
            }
            className={classNames("__wab_instance", sty.iconButton)}
          >
            <GearIcon
              className={classNames(projectcss.all, sty.svg__icCsQ)}
              role={"img"}
            />
          </IconButton>
        }
        addendum={"Blahblah"}
        className={classNames("__wab_instance", sty.listItem, {
          [sty.listItemnested]: hasVariant($state, "nested", "nested"),
          [sty.listItemselected]: hasVariant($state, "selected", "selected"),
          [sty.listItemtype_branch]: hasVariant($state, "type", "branch"),
          [sty.listItemtype_folderClosed]: hasVariant(
            $state,
            "type",
            "folderClosed"
          ),
          [sty.listItemtype_page]: hasVariant($state, "type", "page"),
        })}
        icon={
          <React.Fragment>
            {(hasVariant($state, "type", "folderOpen") ? true : false) ? (
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__q8HG, {
                  [sty.svgselected__q8HGz29Uf]: hasVariant(
                    $state,
                    "selected",
                    "selected"
                  ),
                  [sty.svgtype_folderOpen__q8HG7YhjV]: hasVariant(
                    $state,
                    "type",
                    "folderOpen"
                  ),
                })}
                role={"img"}
              />
            ) : null}
            {(hasVariant($state, "type", "folderClosed") ? true : false) ? (
              <PlasmicIcon__
                PlasmicIconType={
                  hasVariant($state, "type", "folderClosed")
                    ? ChevronRightSvgIcon
                    : ChevronRightSvgIcon
                }
                className={classNames(projectcss.all, sty.svg__g3Nlv, {
                  [sty.svgtype_folderClosed__g3Nlv1OX]: hasVariant(
                    $state,
                    "type",
                    "folderClosed"
                  ),
                  [sty.svgtype_folderOpen__g3Nlv7YhjV]: hasVariant(
                    $state,
                    "type",
                    "folderOpen"
                  ),
                })}
                role={"img"}
              />
            ) : null}
            <PlasmicIcon__
              PlasmicIconType={
                hasVariant($state, "type", "branch")
                  ? GitBranchSvgIcon
                  : hasVariant($state, "type", "folderClosed")
                  ? FolderIcon
                  : hasVariant($state, "type", "folderOpen")
                  ? FolderSvgIcon
                  : hasVariant($state, "type", "arena")
                  ? GridMasonrySvgIcon
                  : hasVariant($state, "type", "page")
                  ? File2SvgIcon
                  : ComponentSvgIcon
              }
              className={classNames(projectcss.all, sty.svg__yn2Mk, {
                [sty.svgselected__yn2MkZ29Uf]: hasVariant(
                  $state,
                  "selected",
                  "selected"
                ),
                [sty.svgtype_arena__yn2Mk2ZahM]: hasVariant(
                  $state,
                  "type",
                  "arena"
                ),
                [sty.svgtype_branch__yn2Mk5W9WS]: hasVariant(
                  $state,
                  "type",
                  "branch"
                ),
                [sty.svgtype_component__yn2MkRp7Wc]: hasVariant(
                  $state,
                  "type",
                  "component"
                ),
                [sty.svgtype_folderClosed__yn2Mk1OX]: hasVariant(
                  $state,
                  "type",
                  "folderClosed"
                ),
                [sty.svgtype_folderOpen__yn2Mk7YhjV]: hasVariant(
                  $state,
                  "type",
                  "folderOpen"
                ),
                [sty.svgtype_page__yn2MkAFmgq]: hasVariant(
                  $state,
                  "type",
                  "page"
                ),
              })}
              role={"img"}
            />
          </React.Fragment>
        }
        isSelected={
          hasVariant($state, "selected", "selected") ? true : undefined
        }
      >
        {renderPlasmicSlot({
          defaultContents: "Item name",
          value: args.children,
        })}
      </ListItem>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "listItem", "iconButton"],
  listItem: ["listItem", "iconButton"],
  iconButton: ["iconButton"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  listItem: typeof ListItem;
  iconButton: typeof IconButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicFolderItem__OverridesType,
  DescendantsType<T>
>;

type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicFolderItem__VariantsArgs;
    args?: PlasmicFolderItem__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicFolderItem__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicFolderItem__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicFolderItem__ArgProps,
          internalVariantPropNames: PlasmicFolderItem__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicFolderItem__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicFolderItem";
  } else {
    func.displayName = `PlasmicFolderItem.${nodeName}`;
  }
  return func;
}

export const PlasmicFolderItem = Object.assign(
  // Top-level PlasmicFolderItem renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    listItem: makeNodeComponent("listItem"),
    iconButton: makeNodeComponent("iconButton"),

    // Metadata about props expected for PlasmicFolderItem
    internalVariantProps: PlasmicFolderItem__VariantProps,
    internalArgProps: PlasmicFolderItem__ArgProps,
  }
);

export default PlasmicFolderItem;
/* prettier-ignore-end */
