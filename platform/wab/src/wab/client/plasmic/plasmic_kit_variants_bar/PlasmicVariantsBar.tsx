// @ts-nocheck
/* eslint-disable */
/* tslint:disable */
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: kdj5vahTyUKxznuR6rrtt6
// Component: 98t4Edcdrb

import * as React from "react";

import * as p from "@plasmicapp/react-web";
import * as ph from "@plasmicapp/react-web/lib/host";

import {
  SingleBooleanChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
} from "@plasmicapp/react-web";
import ToggleRecordingButton from "../../components/canvas/VariantsBar/ToggleRecordingButton"; // plasmic-import: j_Jdg2E_a5/component
import VariantBadge from "../../components/canvas/VariantsBar/VariantBadge"; // plasmic-import: 4OLKnpGnTY/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_design_system_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "./plasmic_plasmic_kit_variants_bar.module.css"; // plasmic-import: kdj5vahTyUKxznuR6rrtt6/projectcss
import sty from "./PlasmicVariantsBar.module.css"; // plasmic-import: 98t4Edcdrb/css

import ChevronDownsvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon

export type PlasmicVariantsBar__VariantMembers = {
  isFocused: "isFocused";
  isEmpty: "isEmpty";
  isEditable: "isEditable";
  contained: "contained";
};

export type PlasmicVariantsBar__VariantsArgs = {
  isFocused?: SingleBooleanChoiceArg<"isFocused">;
  isEmpty?: SingleBooleanChoiceArg<"isEmpty">;
  isEditable?: SingleBooleanChoiceArg<"isEditable">;
  contained?: SingleBooleanChoiceArg<"contained">;
};

type VariantPropType = keyof PlasmicVariantsBar__VariantsArgs;
export const PlasmicVariantsBar__VariantProps = new Array<VariantPropType>(
  "isFocused",
  "isEmpty",
  "isEditable",
  "contained"
);

export type PlasmicVariantsBar__ArgsType = {};
type ArgPropType = keyof PlasmicVariantsBar__ArgsType;
export const PlasmicVariantsBar__ArgProps = new Array<ArgPropType>();

export type PlasmicVariantsBar__OverridesType = {
  root?: p.Flex<"div">;
  recordingButton?: p.Flex<typeof ToggleRecordingButton>;
  freeBox?: p.Flex<"div">;
  variantsList?: p.Flex<"div">;
  dropdownTrigger?: p.Flex<"div">;
  emptyListMessage?: p.Flex<"div">;
  chevronDownIcon?: p.Flex<"div">;
  svg?: p.Flex<"svg">;
};

export interface DefaultVariantsBarProps {
  isFocused?: SingleBooleanChoiceArg<"isFocused">;
  isEmpty?: SingleBooleanChoiceArg<"isEmpty">;
  isEditable?: SingleBooleanChoiceArg<"isEditable">;
  contained?: SingleBooleanChoiceArg<"contained">;
  className?: string;
}

const __wrapUserFunction =
  globalThis.__PlasmicWrapUserFunction ?? ((loc, fn) => fn());
const __wrapUserPromise =
  globalThis.__PlasmicWrapUserPromise ??
  (async (loc, promise) => {
    return await promise;
  });

function PlasmicVariantsBar__RenderFunc(props: {
  variants: PlasmicVariantsBar__VariantsArgs;
  args: PlasmicVariantsBar__ArgsType;
  overrides: PlasmicVariantsBar__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const $ctx = ph.useDataEnv?.() || {};
  const args = React.useMemo(
    () =>
      Object.assign(
        {},

        props.args
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const currentUser = p.useCurrentUser?.() || {};
  const [$queries, setDollarQueries] = React.useState({});
  const stateSpecs = React.useMemo(
    () => [
      {
        path: "isFocused",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isFocused,
      },

      {
        path: "isEmpty",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isEmpty,
      },

      {
        path: "isEditable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isEditable,
      },

      {
        path: "contained",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.contained,
      },
    ],

    [$props, $ctx]
  );
  const $state = p.useDollarState(stateSpecs, { $props, $ctx, $queries });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootcontained]: hasVariant($state, "contained", "contained"),
          [sty.rootcontained_isEditable_isEmpty_isFocused]:
            hasVariant($state, "contained", "contained") &&
            hasVariant($state, "isFocused", "isFocused") &&
            hasVariant($state, "isEditable", "isEditable") &&
            hasVariant($state, "isEmpty", "isEmpty"),
          [sty.rootcontained_isEditable_isFocused]:
            hasVariant($state, "contained", "contained") &&
            hasVariant($state, "isFocused", "isFocused") &&
            hasVariant($state, "isEditable", "isEditable"),
          [sty.rootcontained_isEmpty]:
            hasVariant($state, "isEmpty", "isEmpty") &&
            hasVariant($state, "contained", "contained"),
          [sty.rootcontained_isFocused]:
            hasVariant($state, "contained", "contained") &&
            hasVariant($state, "isFocused", "isFocused"),
          [sty.rootisEditable_isFocused]:
            hasVariant($state, "isFocused", "isFocused") &&
            hasVariant($state, "isEditable", "isEditable"),
          [sty.rootisEmpty]: hasVariant($state, "isEmpty", "isEmpty"),
          [sty.rootisEmpty_isFocused]:
            hasVariant($state, "isFocused", "isFocused") &&
            hasVariant($state, "isEmpty", "isEmpty"),
          [sty.rootisFocused]: hasVariant($state, "isFocused", "isFocused"),
        }
      )}
    >
      {(
        hasVariant($state, "isEmpty", "isEmpty") &&
        hasVariant($state, "isFocused", "isFocused")
          ? false
          : hasVariant($state, "isFocused", "isFocused")
          ? true
          : false
      ) ? (
        <ToggleRecordingButton
          data-plasmic-name={"recordingButton"}
          data-plasmic-override={overrides.recordingButton}
          className={classNames("__wab_instance", sty.recordingButton, {
            [sty.recordingButtoncontained]: hasVariant(
              $state,
              "contained",
              "contained"
            ),
            [sty.recordingButtoncontained_isEditable_isFocused]:
              hasVariant($state, "contained", "contained") &&
              hasVariant($state, "isFocused", "isFocused") &&
              hasVariant($state, "isEditable", "isEditable"),
            [sty.recordingButtoncontained_isFocused]:
              hasVariant($state, "contained", "contained") &&
              hasVariant($state, "isFocused", "isFocused"),
            [sty.recordingButtonisEditable_isEmpty_isFocused]:
              hasVariant($state, "isFocused", "isFocused") &&
              hasVariant($state, "isEmpty", "isEmpty") &&
              hasVariant($state, "isEditable", "isEditable"),
            [sty.recordingButtonisEditable_isFocused]:
              hasVariant($state, "isFocused", "isFocused") &&
              hasVariant($state, "isEditable", "isEditable"),
            [sty.recordingButtonisEmpty]: hasVariant(
              $state,
              "isEmpty",
              "isEmpty"
            ),
            [sty.recordingButtonisEmpty_isFocused]:
              hasVariant($state, "isEmpty", "isEmpty") &&
              hasVariant($state, "isFocused", "isFocused"),
            [sty.recordingButtonisFocused]: hasVariant(
              $state,
              "isFocused",
              "isFocused"
            ),
          })}
          contained={$state.contained}
        />
      ) : null}
      <p.Stack
        as={"div"}
        data-plasmic-name={"freeBox"}
        data-plasmic-override={overrides.freeBox}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox, {
          [sty.freeBoxcontained]: hasVariant($state, "contained", "contained"),
          [sty.freeBoxcontained_isEditable_isFocused]:
            hasVariant($state, "contained", "contained") &&
            hasVariant($state, "isFocused", "isFocused") &&
            hasVariant($state, "isEditable", "isEditable"),
          [sty.freeBoxcontained_isEmpty]:
            hasVariant($state, "isEmpty", "isEmpty") &&
            hasVariant($state, "contained", "contained"),
          [sty.freeBoxcontained_isEmpty_isFocused]:
            hasVariant($state, "isFocused", "isFocused") &&
            hasVariant($state, "contained", "contained") &&
            hasVariant($state, "isEmpty", "isEmpty"),
          [sty.freeBoxcontained_isFocused]:
            hasVariant($state, "contained", "contained") &&
            hasVariant($state, "isFocused", "isFocused"),
          [sty.freeBoxisEditable_isEmpty_isFocused]:
            hasVariant($state, "isFocused", "isFocused") &&
            hasVariant($state, "isEmpty", "isEmpty") &&
            hasVariant($state, "isEditable", "isEditable"),
          [sty.freeBoxisEditable_isFocused]:
            hasVariant($state, "isFocused", "isFocused") &&
            hasVariant($state, "isEditable", "isEditable"),
          [sty.freeBoxisEmpty]: hasVariant($state, "isEmpty", "isEmpty"),
          [sty.freeBoxisEmpty_isFocused]:
            hasVariant($state, "isFocused", "isFocused") &&
            hasVariant($state, "isEmpty", "isEmpty"),
          [sty.freeBoxisFocused]: hasVariant($state, "isFocused", "isFocused"),
        })}
      >
        {(hasVariant($state, "isEmpty", "isEmpty") ? false : true) ? (
          <p.Stack
            as={"div"}
            data-plasmic-name={"variantsList"}
            data-plasmic-override={overrides.variantsList}
            hasGap={true}
            className={classNames(projectcss.all, sty.variantsList, {
              [sty.variantsListcontained_isEditable_isFocused]:
                hasVariant($state, "contained", "contained") &&
                hasVariant($state, "isFocused", "isFocused") &&
                hasVariant($state, "isEditable", "isEditable"),
              [sty.variantsListcontained_isEmpty]:
                hasVariant($state, "isEmpty", "isEmpty") &&
                hasVariant($state, "contained", "contained"),
              [sty.variantsListisEditable_isEmpty_isFocused]:
                hasVariant($state, "isFocused", "isFocused") &&
                hasVariant($state, "isEmpty", "isEmpty") &&
                hasVariant($state, "isEditable", "isEditable"),
              [sty.variantsListisEmpty]: hasVariant(
                $state,
                "isEmpty",
                "isEmpty"
              ),
              [sty.variantsListisFocused]: hasVariant(
                $state,
                "isFocused",
                "isFocused"
              ),
            })}
          >
            <VariantBadge
              className={classNames("__wab_instance", sty.variantBadge__ns9NY, {
                [sty.variantBadgecontained_isEmpty__ns9NY6QeTyOwGPm]:
                  hasVariant($state, "isEmpty", "isEmpty") &&
                  hasVariant($state, "contained", "contained"),
                [sty.variantBadgecontained_isEmpty_isFocused__ns9NY6QeTyOwGPmSmoKc]:
                  hasVariant($state, "isFocused", "isFocused") &&
                  hasVariant($state, "contained", "contained") &&
                  hasVariant($state, "isEmpty", "isEmpty"),
                [sty.variantBadgeisEditable_isFocused__ns9NYEkI4SSmoKc]:
                  hasVariant($state, "isFocused", "isFocused") &&
                  hasVariant($state, "isEditable", "isEditable"),
                [sty.variantBadgeisFocused__ns9NYsmoKc]: hasVariant(
                  $state,
                  "isFocused",
                  "isFocused"
                ),
              })}
              isFocused={
                hasVariant($state, "isFocused", "isFocused") ? true : undefined
              }
              isUnpinnable={
                hasVariant($state, "isFocused", "isFocused") &&
                hasVariant($state, "isEditable", "isEditable")
                  ? true
                  : hasVariant($state, "isFocused", "isFocused")
                  ? undefined
                  : true
              }
            />

            <VariantBadge
              className={classNames("__wab_instance", sty.variantBadge__jdaZf, {
                [sty.variantBadgeisEditable__jdaZfEkI4S]: hasVariant(
                  $state,
                  "isEditable",
                  "isEditable"
                ),
                [sty.variantBadgeisEditable_isFocused__jdaZfEkI4SSmoKc]:
                  hasVariant($state, "isFocused", "isFocused") &&
                  hasVariant($state, "isEditable", "isEditable"),
                [sty.variantBadgeisEmpty__jdaZfOwGPm]: hasVariant(
                  $state,
                  "isEmpty",
                  "isEmpty"
                ),
                [sty.variantBadgeisFocused__jdaZfSmoKc]: hasVariant(
                  $state,
                  "isFocused",
                  "isFocused"
                ),
              })}
              isFocused={
                hasVariant($state, "isFocused", "isFocused") ? true : undefined
              }
              isRecording={true}
              isUnpinnable={
                hasVariant($state, "isFocused", "isFocused") &&
                hasVariant($state, "isEditable", "isEditable")
                  ? true
                  : hasVariant($state, "isFocused", "isFocused")
                  ? undefined
                  : true
              }
            />
          </p.Stack>
        ) : null}
        {(
          hasVariant($state, "isFocused", "isFocused") &&
          hasVariant($state, "isEditable", "isEditable")
            ? true
            : hasVariant($state, "isEditable", "isEditable")
            ? false
            : false
        ) ? (
          <p.Stack
            as={"div"}
            data-plasmic-name={"dropdownTrigger"}
            data-plasmic-override={overrides.dropdownTrigger}
            hasGap={true}
            className={classNames(projectcss.all, sty.dropdownTrigger, {
              [sty.dropdownTriggercontained_isEditable_isEmpty_isFocused]:
                hasVariant($state, "contained", "contained") &&
                hasVariant($state, "isEmpty", "isEmpty") &&
                hasVariant($state, "isFocused", "isFocused") &&
                hasVariant($state, "isEditable", "isEditable"),
              [sty.dropdownTriggercontained_isEditable_isFocused]:
                hasVariant($state, "contained", "contained") &&
                hasVariant($state, "isFocused", "isFocused") &&
                hasVariant($state, "isEditable", "isEditable"),
              [sty.dropdownTriggerisEditable]: hasVariant(
                $state,
                "isEditable",
                "isEditable"
              ),
              [sty.dropdownTriggerisEditable_isEmpty_isFocused]:
                hasVariant($state, "isFocused", "isFocused") &&
                hasVariant($state, "isEmpty", "isEmpty") &&
                hasVariant($state, "isEditable", "isEditable"),
              [sty.dropdownTriggerisEditable_isFocused]:
                hasVariant($state, "isFocused", "isFocused") &&
                hasVariant($state, "isEditable", "isEditable"),
              [sty.dropdownTriggerisEmpty]: hasVariant(
                $state,
                "isEmpty",
                "isEmpty"
              ),
              [sty.dropdownTriggerisEmpty_isFocused]:
                hasVariant($state, "isFocused", "isFocused") &&
                hasVariant($state, "isEmpty", "isEmpty"),
              [sty.dropdownTriggerisFocused]: hasVariant(
                $state,
                "isFocused",
                "isFocused"
              ),
            })}
          >
            {(hasVariant($state, "isEmpty", "isEmpty") ? true : false) ? (
              <div
                data-plasmic-name={"emptyListMessage"}
                data-plasmic-override={overrides.emptyListMessage}
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.emptyListMessage,
                  {
                    [sty.emptyListMessagecontained_isEditable_isEmpty_isFocused]:
                      hasVariant($state, "contained", "contained") &&
                      hasVariant($state, "isEmpty", "isEmpty") &&
                      hasVariant($state, "isFocused", "isFocused") &&
                      hasVariant($state, "isEditable", "isEditable"),
                    [sty.emptyListMessageisEditable_isEmpty_isFocused]:
                      hasVariant($state, "isFocused", "isFocused") &&
                      hasVariant($state, "isEmpty", "isEmpty") &&
                      hasVariant($state, "isEditable", "isEditable"),
                    [sty.emptyListMessageisEmpty]: hasVariant(
                      $state,
                      "isEmpty",
                      "isEmpty"
                    ),
                    [sty.emptyListMessageisFocused]: hasVariant(
                      $state,
                      "isFocused",
                      "isFocused"
                    ),
                  }
                )}
              >
                {"Select variants to record"}
              </div>
            ) : null}
            <div
              data-plasmic-name={"chevronDownIcon"}
              data-plasmic-override={overrides.chevronDownIcon}
              className={classNames(projectcss.all, sty.chevronDownIcon)}
            >
              <ChevronDownsvgIcon
                data-plasmic-name={"svg"}
                data-plasmic-override={overrides.svg}
                className={classNames(projectcss.all, sty.svg, {
                  [sty.svgisEditable]: hasVariant(
                    $state,
                    "isEditable",
                    "isEditable"
                  ),
                  [sty.svgisEditable_isEmpty]:
                    hasVariant($state, "isEmpty", "isEmpty") &&
                    hasVariant($state, "isEditable", "isEditable"),
                  [sty.svgisEditable_isEmpty_isFocused]:
                    hasVariant($state, "isFocused", "isFocused") &&
                    hasVariant($state, "isEmpty", "isEmpty") &&
                    hasVariant($state, "isEditable", "isEditable"),
                  [sty.svgisFocused]: hasVariant(
                    $state,
                    "isFocused",
                    "isFocused"
                  ),
                })}
                role={"img"}
              />
            </div>
          </p.Stack>
        ) : null}
      </p.Stack>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "recordingButton",
    "freeBox",
    "variantsList",
    "dropdownTrigger",
    "emptyListMessage",
    "chevronDownIcon",
    "svg",
  ],

  recordingButton: ["recordingButton"],
  freeBox: [
    "freeBox",
    "variantsList",
    "dropdownTrigger",
    "emptyListMessage",
    "chevronDownIcon",
    "svg",
  ],

  variantsList: ["variantsList"],
  dropdownTrigger: [
    "dropdownTrigger",
    "emptyListMessage",
    "chevronDownIcon",
    "svg",
  ],

  emptyListMessage: ["emptyListMessage"],
  chevronDownIcon: ["chevronDownIcon", "svg"],
  svg: ["svg"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  recordingButton: typeof ToggleRecordingButton;
  freeBox: "div";
  variantsList: "div";
  dropdownTrigger: "div";
  emptyListMessage: "div";
  chevronDownIcon: "div";
  svg: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicVariantsBar__OverridesType,
  DescendantsType<T>
>;

type NodeComponentProps<T extends NodeNameType> = {
  // Explicitly specify variants, args, and overrides as objects
  variants?: PlasmicVariantsBar__VariantsArgs;
  args?: PlasmicVariantsBar__ArgsType;
  overrides?: NodeOverridesType<T>;
} & Omit<PlasmicVariantsBar__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
  // Specify args directly as props
  Omit<PlasmicVariantsBar__ArgsType, ReservedPropsType> &
  // Specify overrides for each element directly as props
  Omit<
    NodeOverridesType<T>,
    ReservedPropsType | VariantPropType | ArgPropType
  > &
  // Specify props for the root element
  Omit<
    Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
    ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
  >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: [...PlasmicDescendants[nodeName]],
          internalArgPropNames: PlasmicVariantsBar__ArgProps,
          internalVariantPropNames: PlasmicVariantsBar__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicVariantsBar__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicVariantsBar";
  } else {
    func.displayName = `PlasmicVariantsBar.${nodeName}`;
  }
  return func;
}

export const PlasmicVariantsBar = Object.assign(
  // Top-level PlasmicVariantsBar renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    recordingButton: makeNodeComponent("recordingButton"),
    freeBox: makeNodeComponent("freeBox"),
    variantsList: makeNodeComponent("variantsList"),
    dropdownTrigger: makeNodeComponent("dropdownTrigger"),
    emptyListMessage: makeNodeComponent("emptyListMessage"),
    chevronDownIcon: makeNodeComponent("chevronDownIcon"),
    svg: makeNodeComponent("svg"),

    // Metadata about props expected for PlasmicVariantsBar
    internalVariantProps: PlasmicVariantsBar__VariantProps,
    internalArgProps: PlasmicVariantsBar__ArgProps,
  }
);

export default PlasmicVariantsBar;
/* prettier-ignore-end */
