// @ts-nocheck
/* eslint-disable */
/* tslint:disable */
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: frhoorZk3bxNXU73uUyvHm
// Component: Coj9xtPv-Oc

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateStateOnChangeProp,
  generateStateValueProp,
  hasVariant,
  renderPlasmicSlot,
  useCurrentUser,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import LabeledItem from "../../components/sidebar-tabs/StateManagement/LabeledItem"; // plasmic-import: EmZVqVuGE1/component
import StyleSelect from "../../components/style-controls/StyleSelect"; // plasmic-import: E0bKgamUEin/component
import StyleSelect__Option from "../../components/style-controls/StyleSelect__Option"; // plasmic-import: fVzKJ6hzd6u/component
import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component
import Switch from "../../components/widgets/Switch"; // plasmic-import: b35JDgXpbiF/component
import Textbox from "../../components/widgets/Textbox"; // plasmic-import: pA22NEzDCsn_/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "./plasmic_plasmic_kit_state_management.module.css"; // plasmic-import: frhoorZk3bxNXU73uUyvHm/projectcss
import sty from "./PlasmicNewVariable.module.css"; // plasmic-import: Coj9xtPv-Oc/css

import ArrowRightsvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ArrowRightSvg"; // plasmic-import: 9Jv8jb253/icon
import ChevronDownsvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import ClosesvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__CloseSvg"; // plasmic-import: DhvEHyCHT/icon
import SearchsvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__SearchSvg"; // plasmic-import: R5DLz11OA/icon

createPlasmicElementProxy;

export type PlasmicNewVariable__VariantMembers = {
  isExternal: "isExternal";
  isImplicitState: "isImplicitState";
  accessType: "_private" | "readonly" | "writable";
  withFormButtons: "withFormButtons";
  isPageComponent: "isPageComponent";
};
export type PlasmicNewVariable__VariantsArgs = {
  isExternal?: SingleBooleanChoiceArg<"isExternal">;
  isImplicitState?: SingleBooleanChoiceArg<"isImplicitState">;
  accessType?: SingleChoiceArg<"_private" | "readonly" | "writable">;
  withFormButtons?: SingleBooleanChoiceArg<"withFormButtons">;
  isPageComponent?: SingleBooleanChoiceArg<"isPageComponent">;
};
type VariantPropType = keyof PlasmicNewVariable__VariantsArgs;
export const PlasmicNewVariable__VariantProps = new Array<VariantPropType>(
  "isExternal",
  "isImplicitState",
  "accessType",
  "withFormButtons",
  "isPageComponent"
);

export type PlasmicNewVariable__ArgsType = {
  variableInitVal?: React.ReactNode;
  variableName?: React.ReactNode;
};
type ArgPropType = keyof PlasmicNewVariable__ArgsType;
export const PlasmicNewVariable__ArgProps = new Array<ArgPropType>(
  "variableInitVal",
  "variableName"
);

export type PlasmicNewVariable__OverridesType = {
  root?: Flex__<"div">;
  variableType?: Flex__<typeof StyleSelect>;
  allowExternalAccess?: Flex__<typeof Switch>;
  accessTypeSelect?: Flex__<typeof StyleSelect>;
  cancelButton?: Flex__<typeof Button>;
  confirmButton?: Flex__<typeof Button>;
};

export interface DefaultNewVariableProps {
  variableInitVal?: React.ReactNode;
  variableName?: React.ReactNode;
  isExternal?: SingleBooleanChoiceArg<"isExternal">;
  isImplicitState?: SingleBooleanChoiceArg<"isImplicitState">;
  accessType?: SingleChoiceArg<"_private" | "readonly" | "writable">;
  withFormButtons?: SingleBooleanChoiceArg<"withFormButtons">;
  isPageComponent?: SingleBooleanChoiceArg<"isPageComponent">;
  className?: string;
}

const $$ = {};

function PlasmicNewVariable__RenderFunc(props: {
  variants: PlasmicNewVariable__VariantsArgs;
  args: PlasmicNewVariable__ArgsType;
  overrides: PlasmicNewVariable__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(() => Object.assign({}, props.args), [props.args]);

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const currentUser = useCurrentUser?.() || {};

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "isExternal",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isExternal,
      },
      {
        path: "isImplicitState",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.isImplicitState,
      },
      {
        path: "accessType",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.accessType,
      },
      {
        path: "withFormButtons",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.withFormButtons,
      },
      {
        path: "isPageComponent",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.isPageComponent,
      },
      {
        path: "allowExternalAccess.isChecked",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          hasVariant($state, "isExternal", "isExternal")
            ? "isChecked"
            : undefined,
      },
      {
        path: "variableType.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "accessTypeSelect.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
    ],

    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <Stack__
      as={"div"}
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootaccessType__private]: hasVariant(
            $state,
            "accessType",
            "_private"
          ),
          [sty.rootaccessType_readonly]: hasVariant(
            $state,
            "accessType",
            "readonly"
          ),
          [sty.rootaccessType_readonly_isImplicitState]:
            hasVariant($state, "isImplicitState", "isImplicitState") &&
            hasVariant($state, "accessType", "readonly"),
          [sty.rootaccessType_writable]: hasVariant(
            $state,
            "accessType",
            "writable"
          ),
          [sty.rootisExternal]: hasVariant($state, "isExternal", "isExternal"),
          [sty.rootisImplicitState]: hasVariant(
            $state,
            "isImplicitState",
            "isImplicitState"
          ),
          [sty.rootisImplicitState_accessType_writable]:
            hasVariant($state, "isImplicitState", "isImplicitState") &&
            hasVariant($state, "accessType", "writable"),
          [sty.rootisPageComponent]: hasVariant(
            $state,
            "isPageComponent",
            "isPageComponent"
          ),
          [sty.rootwithFormButtons]: hasVariant(
            $state,
            "withFormButtons",
            "withFormButtons"
          ),
        }
      )}
    >
      <div
        className={classNames(projectcss.all, sty.freeBox__wMgtQ, {
          [sty.freeBoxaccessType_readonly_isImplicitState__wMgtQ4ApxWWs6WV]:
            hasVariant($state, "isImplicitState", "isImplicitState") &&
            hasVariant($state, "accessType", "readonly"),
          [sty.freeBoxisExternal__wMgtQdBoOy]: hasVariant(
            $state,
            "isExternal",
            "isExternal"
          ),
          [sty.freeBoxisImplicitState__wMgtQws6WV]: hasVariant(
            $state,
            "isImplicitState",
            "isImplicitState"
          ),
          [sty.freeBoxisImplicitState_accessType_writable__wMgtQws6WVL5M6I]:
            hasVariant($state, "isImplicitState", "isImplicitState") &&
            hasVariant($state, "accessType", "writable"),
        })}
      >
        {renderPlasmicSlot({
          defaultContents: (
            <LabeledItem
              className={classNames("__wab_instance", sty.labeledItem___92BR)}
              label={
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text___7B2Br
                  )}
                >
                  {"Variable name"}
                </div>
              }
              layout={"vertical"}
              value={
                <Textbox
                  className={classNames("__wab_instance", sty.textbox__bThkw)}
                  prefixIcon={
                    <SearchsvgIcon
                      className={classNames(projectcss.all, sty.svg__pIVjv)}
                      role={"img"}
                    />
                  }
                  styleType={["bordered"]}
                  suffixIcon={
                    <ClosesvgIcon
                      className={classNames(projectcss.all, sty.svg__sEuY3)}
                      role={"img"}
                    />
                  }
                />
              }
            />
          ),

          value: args.variableName,
        })}
      </div>
      <LabeledItem
        className={classNames("__wab_instance", sty.labeledItem__g3YwR, {
          [sty.labeledItemisImplicitState__g3YwRws6WV]: hasVariant(
            $state,
            "isImplicitState",
            "isImplicitState"
          ),
        })}
        label={"Type"}
        layout={"vertical"}
        value={
          <StyleSelect
            data-plasmic-name={"variableType"}
            data-plasmic-override={overrides.variableType}
            className={classNames("__wab_instance", sty.variableType, {
              [sty.variableTypeisExternal]: hasVariant(
                $state,
                "isExternal",
                "isExternal"
              ),
            })}
            onChange={(...eventArgs) => {
              generateStateOnChangeProp($state, ["variableType", "value"])(
                eventArgs[0]
              );
            }}
            placeholder={"Placeholder\u2026"}
            value={generateStateValueProp($state, ["variableType", "value"])}
            valueSetState={"isSet"}
          >
            <StyleSelect__Option
              className={classNames("__wab_instance", sty.option___1Ws0C)}
              value={"option"}
            >
              {"Option 1"}
            </StyleSelect__Option>
            <StyleSelect__Option
              className={classNames("__wab_instance", sty.option__o2AW2)}
              value={"option"}
            >
              {"Option 2"}
            </StyleSelect__Option>
          </StyleSelect>
        }
      />

      <div
        className={classNames(projectcss.all, sty.freeBox__ndf1U, {
          [sty.freeBoxisExternal__ndf1UdBoOy]: hasVariant(
            $state,
            "isExternal",
            "isExternal"
          ),
          [sty.freeBoxisImplicitState__ndf1Uws6WV]: hasVariant(
            $state,
            "isImplicitState",
            "isImplicitState"
          ),
        })}
      >
        {renderPlasmicSlot({
          defaultContents: (
            <LabeledItem
              className={classNames("__wab_instance", sty.labeledItem__yQqyl)}
              label={"Initial Value"}
              layout={"vertical"}
              value={
                <Textbox
                  className={classNames("__wab_instance", sty.textbox___3LE7F)}
                  prefixIcon={
                    <SearchsvgIcon
                      className={classNames(projectcss.all, sty.svg___0EDqL)}
                      role={"img"}
                    />
                  }
                  styleType={["bordered"]}
                  suffixIcon={
                    <ClosesvgIcon
                      className={classNames(projectcss.all, sty.svg__riKvv)}
                      role={"img"}
                    />
                  }
                />
              }
            />
          ),

          value: args.variableInitVal,
        })}
      </div>
      <Stack__
        as={"div"}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox__knZkb, {
          [sty.freeBoxaccessType__private__knZkb3AJj2]: hasVariant(
            $state,
            "accessType",
            "_private"
          ),
          [sty.freeBoxaccessType_readonly__knZkb4ApxW]: hasVariant(
            $state,
            "accessType",
            "readonly"
          ),
          [sty.freeBoxaccessType_readonly_isImplicitState__knZkb4ApxWWs6WV]:
            hasVariant($state, "isImplicitState", "isImplicitState") &&
            hasVariant($state, "accessType", "readonly"),
          [sty.freeBoxisExternal__knZkBdBoOy]: hasVariant(
            $state,
            "isExternal",
            "isExternal"
          ),
          [sty.freeBoxisImplicitState__knZkbws6WV]: hasVariant(
            $state,
            "isImplicitState",
            "isImplicitState"
          ),
          [sty.freeBoxisImplicitState_accessType_writable__knZkbws6WVL5M6I]:
            hasVariant($state, "isImplicitState", "isImplicitState") &&
            hasVariant($state, "accessType", "writable"),
          [sty.freeBoxisPageComponent__knZkbVaTK]: hasVariant(
            $state,
            "isPageComponent",
            "isPageComponent"
          ),
          [sty.freeBoxwithFormButtons__knZkbFjzBe]: hasVariant(
            $state,
            "withFormButtons",
            "withFormButtons"
          ),
        })}
      >
        <Switch
          data-plasmic-name={"allowExternalAccess"}
          data-plasmic-override={overrides.allowExternalAccess}
          className={classNames("__wab_instance", sty.allowExternalAccess, {
            [sty.allowExternalAccessaccessType__private]: hasVariant(
              $state,
              "accessType",
              "_private"
            ),
            [sty.allowExternalAccessisExternal]: hasVariant(
              $state,
              "isExternal",
              "isExternal"
            ),
            [sty.allowExternalAccessisImplicitState]: hasVariant(
              $state,
              "isImplicitState",
              "isImplicitState"
            ),
          })}
          isChecked={
            generateStateValueProp($state, [
              "allowExternalAccess",
              "isChecked",
            ]) ?? false
          }
          onChange={(...eventArgs) => {
            generateStateOnChangeProp($state, [
              "allowExternalAccess",
              "isChecked",
            ])(eventArgs[0]);
          }}
        >
          {"Allow external access"}
        </Switch>
        <LabeledItem
          className={classNames("__wab_instance", sty.labeledItem__r2Zs6, {
            [sty.labeledItemaccessType__private__r2Zs63AJj2]: hasVariant(
              $state,
              "accessType",
              "_private"
            ),
            [sty.labeledItemaccessType_readonly__r2Zs64ApxW]: hasVariant(
              $state,
              "accessType",
              "readonly"
            ),
            [sty.labeledItemisExternal__r2Zs6DBoOy]: hasVariant(
              $state,
              "isExternal",
              "isExternal"
            ),
          })}
          label={"External access type"}
          layout={"vertical"}
          value={
            <StyleSelect
              data-plasmic-name={"accessTypeSelect"}
              data-plasmic-override={overrides.accessTypeSelect}
              className={classNames("__wab_instance", sty.accessTypeSelect, {
                [sty.accessTypeSelectisExternal]: hasVariant(
                  $state,
                  "isExternal",
                  "isExternal"
                ),
              })}
              onChange={(...eventArgs) => {
                generateStateOnChangeProp($state, [
                  "accessTypeSelect",
                  "value",
                ])(eventArgs[0]);
              }}
              placeholder={"Placeholder\u2026"}
              value={generateStateValueProp($state, [
                "accessTypeSelect",
                "value",
              ])}
              valueSetState={"isSet"}
            >
              <StyleSelect__Option
                className={classNames("__wab_instance", sty.option__v0H15)}
                value={"option"}
              >
                {"Option 1"}
              </StyleSelect__Option>
              <StyleSelect__Option
                className={classNames("__wab_instance", sty.option__tUc3F)}
                value={"option"}
              >
                {"Option 2"}
              </StyleSelect__Option>
            </StyleSelect>
          }
        />
      </Stack__>
      <Stack__
        as={"div"}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox__uirV1, {
          [sty.freeBoxwithFormButtons__uirV1FjzBe]: hasVariant(
            $state,
            "withFormButtons",
            "withFormButtons"
          ),
        })}
      >
        <Button
          data-plasmic-name={"cancelButton"}
          data-plasmic-override={overrides.cancelButton}
          caption={"Caption"}
          className={classNames("__wab_instance", sty.cancelButton)}
          endIcon={
            <ChevronDownsvgIcon
              className={classNames(projectcss.all, sty.svg__iifqa)}
              role={"img"}
            />
          }
          startIcon={
            <ArrowRightsvgIcon
              className={classNames(projectcss.all, sty.svg___5Ghrl)}
              role={"img"}
            />
          }
        >
          {"Cancel"}
        </Button>
        <Button
          data-plasmic-name={"confirmButton"}
          data-plasmic-override={overrides.confirmButton}
          caption={"Caption"}
          className={classNames("__wab_instance", sty.confirmButton)}
          endIcon={
            <ChevronDownsvgIcon
              className={classNames(projectcss.all, sty.svg___4Tkj)}
              role={"img"}
            />
          }
          startIcon={
            <ArrowRightsvgIcon
              className={classNames(projectcss.all, sty.svg___3JWvy)}
              role={"img"}
            />
          }
          type={["primary"]}
        >
          {"Confirm"}
        </Button>
      </Stack__>
    </Stack__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "variableType",
    "allowExternalAccess",
    "accessTypeSelect",
    "cancelButton",
    "confirmButton",
  ],

  variableType: ["variableType"],
  allowExternalAccess: ["allowExternalAccess"],
  accessTypeSelect: ["accessTypeSelect"],
  cancelButton: ["cancelButton"],
  confirmButton: ["confirmButton"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  variableType: typeof StyleSelect;
  allowExternalAccess: typeof Switch;
  accessTypeSelect: typeof StyleSelect;
  cancelButton: typeof Button;
  confirmButton: typeof Button;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicNewVariable__OverridesType,
  DescendantsType<T>
>;

type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicNewVariable__VariantsArgs;
    args?: PlasmicNewVariable__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicNewVariable__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicNewVariable__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicNewVariable__ArgProps,
          internalVariantPropNames: PlasmicNewVariable__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicNewVariable__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicNewVariable";
  } else {
    func.displayName = `PlasmicNewVariable.${nodeName}`;
  }
  return func;
}

export const PlasmicNewVariable = Object.assign(
  // Top-level PlasmicNewVariable renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    variableType: makeNodeComponent("variableType"),
    allowExternalAccess: makeNodeComponent("allowExternalAccess"),
    accessTypeSelect: makeNodeComponent("accessTypeSelect"),
    cancelButton: makeNodeComponent("cancelButton"),
    confirmButton: makeNodeComponent("confirmButton"),

    // Metadata about props expected for PlasmicNewVariable
    internalVariantProps: PlasmicNewVariable__VariantProps,
    internalArgProps: PlasmicNewVariable__ArgProps,
  }
);

export default PlasmicNewVariable;
/* prettier-ignore-end */
