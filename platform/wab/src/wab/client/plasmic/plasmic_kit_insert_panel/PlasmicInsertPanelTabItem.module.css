.root {
  transition-property: all;
  transition-duration: 0.15s;
  display: flex;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  position: relative;
  cursor: pointer;
  min-width: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.15s;
  border-radius: 4px;
  padding: 8px;
}
.root > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  min-width: 0;
  margin-left: calc(0px - 4px);
  width: calc(100% + 4px);
}
.root > :global(.__wab_flex-container) > *,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.root > :global(.__wab_flex-container) > picture > img,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > picture > img {
  margin-left: 4px;
}
.rootisSelected {
  background: var(--token-ixk0760-E);
}
.root:hover {
  background: var(--token-bV4cCeIniS6);
}
.rootisSelected:hover {
  background: var(--token-RhvOnhv_xIi);
}
.freeBox___7UjJc {
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 16px;
  height: 16px;
  max-width: 100%;
  flex-shrink: 0;
  display: none;
}
.freeBoxhasIcon___7UjJcfhJht {
  display: flex;
}
.svg__ocCw {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.freeBox__on6ED {
  width: 100%;
  height: auto;
  max-width: 800px;
  display: flex;
  flex-direction: row;
  min-width: 0;
}
.slotTargetChildren {
  font-size: 12px;
}
.slotTargetChildrenisSelected {
  color: var(--token-9i-6NBfNr);
  font-weight: 500;
}
