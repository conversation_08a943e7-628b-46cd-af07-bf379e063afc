.root {
  transition-property: all;
  transition-duration: 0.15s;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;
  max-width: 100%;
  position: relative;
  cursor: pointer;
  min-width: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.15s;
  border-radius: 4px;
  padding: 8px;
}
.root > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 0;
  margin-top: calc(0px - 4px);
  height: calc(100% + 4px);
}
.root > :global(.__wab_flex-container) > *,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.root > :global(.__wab_flex-container) > picture > img,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > picture > img {
  margin-top: 4px;
}
.rootisSelected {
  background: var(--token-ixk0760-E);
}
.root:hover {
  background: #f0f0f0;
}
.rootisSelected:hover {
  background: var(--token-ixk0760-E);
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 24px;
  height: 24px;
  max-width: 100%;
  flex-shrink: 0;
}
.slotTargetIcon {
  color: var(--token-fVn5vRhXJxQ);
}
.slotTargetIconisSelected {
  color: var(--token-9i-6NBfNr);
}
.svg___64T5I {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.slotTargetChildren {
  font-size: 12px;
}
.slotTargetChildrenisSelected {
  color: var(--token-9i-6NBfNr);
}
