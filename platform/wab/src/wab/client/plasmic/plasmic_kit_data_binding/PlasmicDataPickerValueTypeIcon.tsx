/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: w2GXN278dkQ2gQTVQnPehW
// Component: gWylXtol8Lf

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "./plasmic_plasmic_kit_data_binding.module.css"; // plasmic-import: w2GXN278dkQ2gQTVQnPehW/projectcss
import sty from "./PlasmicDataPickerValueTypeIcon.module.css"; // plasmic-import: gWylXtol8Lf/css

createPlasmicElementProxy;

export type PlasmicDataPickerValueTypeIcon__VariantMembers = {
  isSelected: "isSelected";
  isHovered: "isHovered";
};
export type PlasmicDataPickerValueTypeIcon__VariantsArgs = {
  isSelected?: SingleBooleanChoiceArg<"isSelected">;
  isHovered?: SingleBooleanChoiceArg<"isHovered">;
};
type VariantPropType = keyof PlasmicDataPickerValueTypeIcon__VariantsArgs;
export const PlasmicDataPickerValueTypeIcon__VariantProps =
  new Array<VariantPropType>("isSelected", "isHovered");

export type PlasmicDataPickerValueTypeIcon__ArgsType = {
  children?: React.ReactNode;
};
type ArgPropType = keyof PlasmicDataPickerValueTypeIcon__ArgsType;
export const PlasmicDataPickerValueTypeIcon__ArgProps = new Array<ArgPropType>(
  "children"
);

export type PlasmicDataPickerValueTypeIcon__OverridesType = {
  root?: Flex__<"div">;
};

export interface DefaultDataPickerValueTypeIconProps {
  children?: React.ReactNode;
  isSelected?: SingleBooleanChoiceArg<"isSelected">;
  isHovered?: SingleBooleanChoiceArg<"isHovered">;
  className?: string;
}

const $$ = {};

function PlasmicDataPickerValueTypeIcon__RenderFunc(props: {
  variants: PlasmicDataPickerValueTypeIcon__VariantsArgs;
  args: PlasmicDataPickerValueTypeIcon__ArgsType;
  overrides: PlasmicDataPickerValueTypeIcon__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "isSelected",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isSelected,
      },
      {
        path: "isHovered",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isHovered,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootisHovered]: hasVariant($state, "isHovered", "isHovered"),
          [sty.rootisSelected]: hasVariant($state, "isSelected", "isSelected"),
        }
      )}
    >
      <div
        className={classNames(projectcss.all, sty.freeBox__x0W5L, {
          [sty.freeBoxisSelected__x0W5LqXBw1]: hasVariant(
            $state,
            "isSelected",
            "isSelected"
          ),
        })}
      >
        <div
          className={classNames(projectcss.all, sty.freeBox___1FNcp, {
            [sty.freeBoxisSelected___1FNcpqXBw1]: hasVariant(
              $state,
              "isSelected",
              "isSelected"
            ),
          })}
        >
          {renderPlasmicSlot({
            defaultContents: "S",
            value: args.children,
            className: classNames(sty.slotTargetChildren, {
              [sty.slotTargetChildrenisHovered]: hasVariant(
                $state,
                "isHovered",
                "isHovered"
              ),
              [sty.slotTargetChildrenisSelected]: hasVariant(
                $state,
                "isSelected",
                "isSelected"
              ),
            }),
          })}
        </div>
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicDataPickerValueTypeIcon__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicDataPickerValueTypeIcon__VariantsArgs;
    args?: PlasmicDataPickerValueTypeIcon__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicDataPickerValueTypeIcon__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicDataPickerValueTypeIcon__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicDataPickerValueTypeIcon__ArgProps,
          internalVariantPropNames:
            PlasmicDataPickerValueTypeIcon__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicDataPickerValueTypeIcon__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicDataPickerValueTypeIcon";
  } else {
    func.displayName = `PlasmicDataPickerValueTypeIcon.${nodeName}`;
  }
  return func;
}

export const PlasmicDataPickerValueTypeIcon = Object.assign(
  // Top-level PlasmicDataPickerValueTypeIcon renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicDataPickerValueTypeIcon
    internalVariantProps: PlasmicDataPickerValueTypeIcon__VariantProps,
    internalArgProps: PlasmicDataPickerValueTypeIcon__ArgProps,
  }
);

export default PlasmicDataPickerValueTypeIcon;
/* prettier-ignore-end */
