/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: 6CrqkTcB6gSAHoA8c8zpNz
// Component: tNBvs5bIAy

import * as React from "react";

import {
  Flex as Flex__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateStateOnChangeProp,
  generateStateValueProp,
  hasVariant,
  useDollarState,
  useTrigger,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import AiButton from "../../components/AiButton"; // plasmic-import: BqSsxlQdj2F0/component
import CommentButton from "../../components/CommentButton"; // plasmic-import: JnxWw8hitac/component
import FreeTrial from "../../components/FreeTrial"; // plasmic-import: p3GgKAlaQe/component
import ViewAsButton from "../../components/app-auth/ViewAsButton"; // plasmic-import: MJoB9g7giNL/component
import ArenaSwitcher from "../../components/top-bar/ArenaSwitcher"; // plasmic-import: OAMl2pw5C9W/component
import BranchSwitcher from "../../components/top-bar/BranchSwitcher"; // plasmic-import: IQWpmX8J3t/component
import CodeButton from "../../components/top-bar/CodeButton"; // plasmic-import: FCNHcPh1ZR/component
import LivePopOutButton from "../../components/top-bar/LivePopOutButton"; // plasmic-import: ND5ZuEZMUe/component
import PublishButton from "../../components/top-bar/PublishButton"; // plasmic-import: yXRcEjTceQ/component
import SaveIndicator from "../../components/top-bar/SaveIndicator"; // plasmic-import: HYPZr2nWSgs/component
import ShareButton from "../../components/top-bar/ShareButton"; // plasmic-import: mnPFthIw2I/component
import VariantsComboSelect from "../../components/top-bar/VariantsComboSelect"; // plasmic-import: I6gjdy639O/component
import ViewButton from "../../components/top-bar/ViewButton"; // plasmic-import: -r2DBYss6/component
import ZoomButton from "../../components/top-bar/ZoomButton"; // plasmic-import: UsJCR-Jtn5/component
import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component
import IconButton from "../../components/widgets/IconButton"; // plasmic-import: LPry-TF4j22a/component
import MenuButton from "../../components/widgets/MenuButton"; // plasmic-import: h69wHrrKtL/component
import Select from "../../components/widgets/Select"; // plasmic-import: j_4IQyOWK2b/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import sty from "./PlasmicTopBar.module.css"; // plasmic-import: tNBvs5bIAy/css
import projectcss from "./plasmic_plasmic_kit_top_bar.module.css"; // plasmic-import: 6CrqkTcB6gSAHoA8c8zpNz/projectcss

import InfoIcon from "../plasmic_kit/PlasmicIcon__Info"; // plasmic-import: BjAly3N4fWuWe/icon
import MarkIcon from "../plasmic_kit_design_system/PlasmicIcon__Mark"; // plasmic-import: a6KJVu0om/icon
import MarkFullColorIcon from "../plasmic_kit_design_system/PlasmicIcon__MarkFullColor"; // plasmic-import: l_n_OBLJg/icon
import ArrowLeftSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ArrowLeftSvg"; // plasmic-import: -d8Kjj4sp/icon
import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import PlusSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__PlusSvg"; // plasmic-import: sQKgd2GNr/icon
import IconIcon from "./icons/PlasmicIcon__Icon"; // plasmic-import: jnoWwmZ86t/icon
import SegmentSeparatorIcon from "./icons/PlasmicIcon__SegmentSeparator"; // plasmic-import: yzguJQZL37/icon
import imageAy93RhuWbc from "./images/image.svg"; // plasmic-import: ay93RhuWbc/picture

createPlasmicElementProxy;

export type PlasmicTopBar__VariantMembers = {
  mode: "preview";
  arenaType: "mixed" | "component" | "page";
  hideAvatar: "hideAvatar";
  plasmicAdminMode: "plasmicAdminMode";
};
export type PlasmicTopBar__VariantsArgs = {
  mode?: SingleChoiceArg<"preview">;
  arenaType?: SingleChoiceArg<"mixed" | "component" | "page">;
  hideAvatar?: SingleBooleanChoiceArg<"hideAvatar">;
  plasmicAdminMode?: SingleBooleanChoiceArg<"plasmicAdminMode">;
};
type VariantPropType = keyof PlasmicTopBar__VariantsArgs;
export const PlasmicTopBar__VariantProps = new Array<VariantPropType>(
  "mode",
  "arenaType",
  "hideAvatar",
  "plasmicAdminMode"
);

export type PlasmicTopBar__ArgsType = {};
type ArgPropType = keyof PlasmicTopBar__ArgsType;
export const PlasmicTopBar__ArgProps = new Array<ArgPropType>();

export type PlasmicTopBar__OverridesType = {
  root?: Flex__<"div">;
  left?: Flex__<"div">;
  logoLink?: Flex__<"a">;
  titleSegment?: Flex__<"div">;
  projectTitle?: Flex__<"div">;
  projectMenu?: Flex__<typeof MenuButton>;
  saveIndicator?: Flex__<typeof SaveIndicator>;
  branchSeparator?: Flex__<"svg">;
  branchSegment?: Flex__<"div">;
  branchSwitcher?: Flex__<typeof BranchSwitcher>;
  arenaSeparator?: Flex__<"svg">;
  arenaSegment?: Flex__<"div">;
  arenaSwitcher?: Flex__<typeof ArenaSwitcher>;
  previewSelect?: Flex__<typeof Select>;
  variantsComboSelect?: Flex__<typeof VariantsComboSelect>;
  centerLive?: Flex__<"div">;
  right?: Flex__<"div">;
  freeTrial?: Flex__<typeof FreeTrial>;
  viewAsButton?: Flex__<typeof ViewAsButton>;
  zoomButton?: Flex__<typeof ZoomButton>;
  viewButton?: Flex__<typeof ViewButton>;
  play?: Flex__<typeof IconButton>;
  livePopOutButton?: Flex__<typeof LivePopOutButton>;
  commentButton?: Flex__<typeof CommentButton>;
  aiButton?: Flex__<typeof AiButton>;
  codeButton?: Flex__<typeof CodeButton>;
  shareButton?: Flex__<typeof ShareButton>;
  publishButton?: Flex__<typeof PublishButton>;
  stop?: Flex__<typeof Button>;
  avatar?: Flex__<typeof PlasmicImg__>;
};

export interface DefaultTopBarProps {
  mode?: SingleChoiceArg<"preview">;
  arenaType?: SingleChoiceArg<"mixed" | "component" | "page">;
  hideAvatar?: SingleBooleanChoiceArg<"hideAvatar">;
  plasmicAdminMode?: SingleBooleanChoiceArg<"plasmicAdminMode">;
  className?: string;
}

const $$ = {};

function PlasmicTopBar__RenderFunc(props: {
  variants: PlasmicTopBar__VariantsArgs;
  args: PlasmicTopBar__ArgsType;
  overrides: PlasmicTopBar__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "mode",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.mode,
      },
      {
        path: "arenaType",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.arenaType,
      },
      {
        path: "hideAvatar",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hideAvatar,
      },
      {
        path: "previewSelect.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined,
      },
      {
        path: "plasmicAdminMode",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.plasmicAdminMode,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  const [isN54740233Hover, triggerN54740233HoverProps] = useTrigger(
    "useHover",
    {}
  );
  const triggers = {
    hover_54740233: isN54740233Hover,
  };

  return (
    <Stack__
      as={"div"}
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootarenaType_component]: hasVariant(
            $state,
            "arenaType",
            "component"
          ),
          [sty.rootarenaType_mixed]: hasVariant($state, "arenaType", "mixed"),
          [sty.rootarenaType_page]: hasVariant($state, "arenaType", "page"),
          [sty.roothideAvatar]: hasVariant($state, "hideAvatar", "hideAvatar"),
          [sty.rootmode_preview]: hasVariant($state, "mode", "preview"),
          [sty.rootplasmicAdminMode]: hasVariant(
            $state,
            "plasmicAdminMode",
            "plasmicAdminMode"
          ),
        }
      )}
    >
      <Stack__
        as={"div"}
        data-plasmic-name={"left"}
        data-plasmic-override={overrides.left}
        hasGap={true}
        className={classNames(projectcss.all, sty.left, {
          [sty.leftarenaType_page]: hasVariant($state, "arenaType", "page"),
          [sty.leftmode_preview]: hasVariant($state, "mode", "preview"),
          [sty.leftplasmicAdminMode]: hasVariant(
            $state,
            "plasmicAdminMode",
            "plasmicAdminMode"
          ),
        })}
      >
        <div className={classNames(projectcss.all, sty.freeBox___7MgoJ)}>
          <PlasmicLink__
            data-plasmic-name={"logoLink"}
            data-plasmic-override={overrides.logoLink}
            className={classNames(projectcss.all, projectcss.a, sty.logoLink, {
              [sty.logoLinkmode_preview]: hasVariant($state, "mode", "preview"),
            })}
            platform={"react"}
          >
            <PlasmicIcon__
              PlasmicIconType={
                triggers.hover_54740233 ? MarkFullColorIcon : MarkIcon
              }
              className={classNames(projectcss.all, sty.svg__jQrU5, {
                [sty.svgmode_preview__jQrU5QMfAz]: hasVariant(
                  $state,
                  "mode",
                  "preview"
                ),
              })}
              role={"img"}
              data-plasmic-trigger-props={[triggerN54740233HoverProps]}
            />
          </PlasmicLink__>
        </div>
        <div
          data-plasmic-name={"titleSegment"}
          data-plasmic-override={overrides.titleSegment}
          className={classNames(projectcss.all, sty.titleSegment)}
        >
          <div
            data-plasmic-name={"projectTitle"}
            data-plasmic-override={overrides.projectTitle}
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.projectTitle,
              {
                [sty.projectTitlemode_preview]: hasVariant(
                  $state,
                  "mode",
                  "preview"
                ),
              }
            )}
          >
            {"Project Name"}
          </div>
          <MenuButton
            data-plasmic-name={"projectMenu"}
            data-plasmic-override={overrides.projectMenu}
            className={classNames("__wab_instance", sty.projectMenu)}
          />

          <SaveIndicator
            data-plasmic-name={"saveIndicator"}
            data-plasmic-override={overrides.saveIndicator}
            className={classNames("__wab_instance", sty.saveIndicator)}
          />
        </div>
        <SegmentSeparatorIcon
          data-plasmic-name={"branchSeparator"}
          data-plasmic-override={overrides.branchSeparator}
          className={classNames(projectcss.all, sty.branchSeparator)}
          role={"img"}
        />

        <div
          data-plasmic-name={"branchSegment"}
          data-plasmic-override={overrides.branchSegment}
          className={classNames(projectcss.all, sty.branchSegment)}
        >
          <BranchSwitcher
            data-plasmic-name={"branchSwitcher"}
            data-plasmic-override={overrides.branchSwitcher}
            className={classNames("__wab_instance", sty.branchSwitcher)}
          />
        </div>
        <SegmentSeparatorIcon
          data-plasmic-name={"arenaSeparator"}
          data-plasmic-override={overrides.arenaSeparator}
          className={classNames(projectcss.all, sty.arenaSeparator)}
          role={"img"}
        />

        {(hasVariant($state, "mode", "preview") ? false : true) ? (
          <div
            data-plasmic-name={"arenaSegment"}
            data-plasmic-override={overrides.arenaSegment}
            className={classNames(projectcss.all, sty.arenaSegment, {
              [sty.arenaSegmentmode_preview]: hasVariant(
                $state,
                "mode",
                "preview"
              ),
            })}
          >
            <ArenaSwitcher
              data-plasmic-name={"arenaSwitcher"}
              data-plasmic-override={overrides.arenaSwitcher}
              className={classNames("__wab_instance", sty.arenaSwitcher, {
                [sty.arenaSwitchermode_preview]: hasVariant(
                  $state,
                  "mode",
                  "preview"
                ),
              })}
            />
          </div>
        ) : null}
        {(hasVariant($state, "mode", "preview") ? true : false) ? (
          <Select
            data-plasmic-name={"previewSelect"}
            data-plasmic-override={overrides.previewSelect}
            className={classNames("__wab_instance", sty.previewSelect, {
              [sty.previewSelectmode_preview]: hasVariant(
                $state,
                "mode",
                "preview"
              ),
            })}
            font={["bold"]}
            icon={
              <PlusSvgIcon
                className={classNames(projectcss.all, sty.svg__aw2Im)}
                role={"img"}
              />
            }
            onChange={async (...eventArgs: any) => {
              ((...eventArgs) => {
                generateStateOnChangeProp($state, ["previewSelect", "value"])(
                  eventArgs[0]
                );
              }).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            placeholder={
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__yJyck
                )}
              >
                {"Select\u2026"}
              </div>
            }
            value={generateStateValueProp($state, ["previewSelect", "value"])}
          />
        ) : null}
        {(hasVariant($state, "mode", "preview") ? true : false) ? (
          <SegmentSeparatorIcon
            className={classNames(projectcss.all, sty.svg__u6BdQ, {
              [sty.svgmode_preview__u6BdQqMfAz]: hasVariant(
                $state,
                "mode",
                "preview"
              ),
            })}
            role={"img"}
          />
        ) : null}
        {(hasVariant($state, "mode", "preview") ? true : false) ? (
          <VariantsComboSelect
            data-plasmic-name={"variantsComboSelect"}
            data-plasmic-override={overrides.variantsComboSelect}
            className={classNames("__wab_instance", sty.variantsComboSelect, {
              [sty.variantsComboSelectmode_preview]: hasVariant(
                $state,
                "mode",
                "preview"
              ),
            })}
          >
            {"Base"}
          </VariantsComboSelect>
        ) : null}
      </Stack__>
      <div
        className={classNames(projectcss.all, sty.freeBox__ab66I, {
          [sty.freeBoxplasmicAdminMode__ab66IGagJ]: hasVariant(
            $state,
            "plasmicAdminMode",
            "plasmicAdminMode"
          ),
        })}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__g59UJ
          )}
        >
          <React.Fragment>
            <span
              className={"plasmic_default__all plasmic_default__span"}
              style={{ fontWeight: 700 }}
            >
              {"admin mode"}
            </span>
            <React.Fragment>{" is disabled"}</React.Fragment>
          </React.Fragment>
        </div>
      </div>
      {(hasVariant($state, "mode", "preview") ? true : false) ? (
        <Stack__
          as={"div"}
          data-plasmic-name={"centerLive"}
          data-plasmic-override={overrides.centerLive}
          hasGap={true}
          className={classNames(projectcss.all, sty.centerLive, {
            [sty.centerLivemode_preview]: hasVariant($state, "mode", "preview"),
            [sty.centerLiveplasmicAdminMode]: hasVariant(
              $state,
              "plasmicAdminMode",
              "plasmicAdminMode"
            ),
          })}
        />
      ) : null}
      <Stack__
        as={"div"}
        data-plasmic-name={"right"}
        data-plasmic-override={overrides.right}
        hasGap={true}
        className={classNames(projectcss.all, sty.right, {
          [sty.rightmode_preview]: hasVariant($state, "mode", "preview"),
          [sty.rightplasmicAdminMode]: hasVariant(
            $state,
            "plasmicAdminMode",
            "plasmicAdminMode"
          ),
        })}
      >
        <FreeTrial
          data-plasmic-name={"freeTrial"}
          data-plasmic-override={overrides.freeTrial}
          className={classNames("__wab_instance", sty.freeTrial)}
          freeTrialTimeRemaining={"0 days left"}
          topBar={true}
        />

        <InfoIcon
          className={classNames(projectcss.all, sty.svg__wdih6, {
            [sty.svgplasmicAdminMode__wdih6GagJ]: hasVariant(
              $state,
              "plasmicAdminMode",
              "plasmicAdminMode"
            ),
          })}
          role={"img"}
        />

        <ViewAsButton
          data-plasmic-name={"viewAsButton"}
          data-plasmic-override={overrides.viewAsButton}
          className={classNames("__wab_instance", sty.viewAsButton)}
        />

        {(hasVariant($state, "mode", "preview") ? false : true) ? (
          <ZoomButton
            data-plasmic-name={"zoomButton"}
            data-plasmic-override={overrides.zoomButton}
            className={classNames("__wab_instance", sty.zoomButton, {
              [sty.zoomButtonmode_preview]: hasVariant(
                $state,
                "mode",
                "preview"
              ),
            })}
          />
        ) : null}
        {(hasVariant($state, "mode", "preview") ? false : true) ? (
          <ViewButton
            data-plasmic-name={"viewButton"}
            data-plasmic-override={overrides.viewButton}
            className={classNames("__wab_instance", sty.viewButton, {
              [sty.viewButtonmode_preview]: hasVariant(
                $state,
                "mode",
                "preview"
              ),
            })}
            mode={hasVariant($state, "mode", "preview") ? "live" : undefined}
          />
        ) : null}
        <div
          className={classNames(projectcss.all, sty.freeBox__wHg9U, {
            [sty.freeBoxmode_preview__wHg9UqMfAz]: hasVariant(
              $state,
              "mode",
              "preview"
            ),
          })}
        >
          {(hasVariant($state, "mode", "preview") ? false : true) ? (
            <IconButton
              data-plasmic-name={"play"}
              data-plasmic-override={overrides.play}
              children2={
                <ChevronDownSvgIcon
                  className={classNames(projectcss.all, sty.svg__erE2Y)}
                  role={"img"}
                />
              }
              className={classNames("__wab_instance", sty.play, {
                [sty.playmode_preview]: hasVariant($state, "mode", "preview"),
              })}
              size={"medium"}
              withGreenBackgroundHover={true}
            >
              <IconIcon
                className={classNames(projectcss.all, sty.svg__amc5N, {
                  [sty.svgmode_preview__amc5NqMfAz]: hasVariant(
                    $state,
                    "mode",
                    "preview"
                  ),
                })}
                role={"img"}
              />
            </IconButton>
          ) : null}
          <LivePopOutButton
            data-plasmic-name={"livePopOutButton"}
            data-plasmic-override={overrides.livePopOutButton}
            className={classNames("__wab_instance", sty.livePopOutButton, {
              [sty.livePopOutButtonmode_preview]: hasVariant(
                $state,
                "mode",
                "preview"
              ),
            })}
          />

          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__tYUhw, {
              [sty.freeBoxmode_preview__tYUhwQMfAz]: hasVariant(
                $state,
                "mode",
                "preview"
              ),
            })}
          >
            <CommentButton
              data-plasmic-name={"commentButton"}
              data-plasmic-override={overrides.commentButton}
              className={classNames("__wab_instance", sty.commentButton, {
                [sty.commentButtonmode_preview]: hasVariant(
                  $state,
                  "mode",
                  "preview"
                ),
              })}
            />

            <AiButton
              data-plasmic-name={"aiButton"}
              data-plasmic-override={overrides.aiButton}
              className={classNames("__wab_instance", sty.aiButton, {
                [sty.aiButtonmode_preview]: hasVariant(
                  $state,
                  "mode",
                  "preview"
                ),
              })}
            />
          </Stack__>
        </div>
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox___3DoVm)}
        >
          <CodeButton
            data-plasmic-name={"codeButton"}
            data-plasmic-override={overrides.codeButton}
            className={classNames("__wab_instance", {
              [sty.codeButtonmode_preview]: hasVariant(
                $state,
                "mode",
                "preview"
              ),
            })}
          />

          {(hasVariant($state, "mode", "preview") ? false : true) ? (
            <ShareButton
              data-plasmic-name={"shareButton"}
              data-plasmic-override={overrides.shareButton}
              className={classNames("__wab_instance", sty.shareButton, {
                [sty.shareButtonmode_preview]: hasVariant(
                  $state,
                  "mode",
                  "preview"
                ),
              })}
            />
          ) : null}
          {(hasVariant($state, "mode", "preview") ? false : true) ? (
            <PublishButton
              data-plasmic-name={"publishButton"}
              data-plasmic-override={overrides.publishButton}
              className={classNames("__wab_instance", sty.publishButton, {
                [sty.publishButtonhideAvatar]: hasVariant(
                  $state,
                  "hideAvatar",
                  "hideAvatar"
                ),
                [sty.publishButtonmode_preview]: hasVariant(
                  $state,
                  "mode",
                  "preview"
                ),
              })}
            />
          ) : null}
        </Stack__>
        {(hasVariant($state, "mode", "preview") ? true : false) ? (
          <Button
            data-plasmic-name={"stop"}
            data-plasmic-override={overrides.stop}
            className={classNames("__wab_instance", sty.stop, {
              [sty.stopmode_preview]: hasVariant($state, "mode", "preview"),
            })}
            endIcon={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__c2B4M)}
                role={"img"}
              />
            }
            size={"wide"}
            startIcon={
              <PlasmicIcon__
                PlasmicIconType={
                  hasVariant($state, "mode", "preview")
                    ? ArrowLeftSvgIcon
                    : ArrowLeftSvgIcon
                }
                className={classNames(projectcss.all, sty.svg__tF90T, {
                  [sty.svgmode_preview__tF90TQMfAz]: hasVariant(
                    $state,
                    "mode",
                    "preview"
                  ),
                })}
                role={"img"}
              />
            }
            type={["secondary"]}
            withIcons={["startIcon"]}
          >
            {hasVariant($state, "mode", "preview")
              ? "Back to studio"
              : "Exit preview"}
          </Button>
        ) : null}
        {(
          hasVariant($state, "hideAvatar", "hideAvatar")
            ? false
            : hasVariant($state, "mode", "preview")
            ? false
            : true
        ) ? (
          <PlasmicImg__
            data-plasmic-name={"avatar"}
            data-plasmic-override={overrides.avatar}
            alt={""}
            className={classNames(sty.avatar, {
              [sty.avatararenaType_page]: hasVariant(
                $state,
                "arenaType",
                "page"
              ),
              [sty.avatarhideAvatar]: hasVariant(
                $state,
                "hideAvatar",
                "hideAvatar"
              ),
              [sty.avatarmode_preview]: hasVariant($state, "mode", "preview"),
            })}
            displayHeight={"1.5rem"}
            displayMaxHeight={"none"}
            displayMaxWidth={"none"}
            displayMinHeight={"0"}
            displayMinWidth={"0"}
            displayWidth={"auto"}
            src={{
              src: imageAy93RhuWbc,
              fullWidth: 150,
              fullHeight: 150,
              aspectRatio: 1,
            }}
          />
        ) : null}
      </Stack__>
    </Stack__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "left",
    "logoLink",
    "titleSegment",
    "projectTitle",
    "projectMenu",
    "saveIndicator",
    "branchSeparator",
    "branchSegment",
    "branchSwitcher",
    "arenaSeparator",
    "arenaSegment",
    "arenaSwitcher",
    "previewSelect",
    "variantsComboSelect",
    "centerLive",
    "right",
    "freeTrial",
    "viewAsButton",
    "zoomButton",
    "viewButton",
    "play",
    "livePopOutButton",
    "commentButton",
    "aiButton",
    "codeButton",
    "shareButton",
    "publishButton",
    "stop",
    "avatar",
  ],
  left: [
    "left",
    "logoLink",
    "titleSegment",
    "projectTitle",
    "projectMenu",
    "saveIndicator",
    "branchSeparator",
    "branchSegment",
    "branchSwitcher",
    "arenaSeparator",
    "arenaSegment",
    "arenaSwitcher",
    "previewSelect",
    "variantsComboSelect",
  ],
  logoLink: ["logoLink"],
  titleSegment: [
    "titleSegment",
    "projectTitle",
    "projectMenu",
    "saveIndicator",
  ],
  projectTitle: ["projectTitle"],
  projectMenu: ["projectMenu"],
  saveIndicator: ["saveIndicator"],
  branchSeparator: ["branchSeparator"],
  branchSegment: ["branchSegment", "branchSwitcher"],
  branchSwitcher: ["branchSwitcher"],
  arenaSeparator: ["arenaSeparator"],
  arenaSegment: ["arenaSegment", "arenaSwitcher"],
  arenaSwitcher: ["arenaSwitcher"],
  previewSelect: ["previewSelect"],
  variantsComboSelect: ["variantsComboSelect"],
  centerLive: ["centerLive"],
  right: [
    "right",
    "freeTrial",
    "viewAsButton",
    "zoomButton",
    "viewButton",
    "play",
    "livePopOutButton",
    "commentButton",
    "aiButton",
    "codeButton",
    "shareButton",
    "publishButton",
    "stop",
    "avatar",
  ],
  freeTrial: ["freeTrial"],
  viewAsButton: ["viewAsButton"],
  zoomButton: ["zoomButton"],
  viewButton: ["viewButton"],
  play: ["play"],
  livePopOutButton: ["livePopOutButton"],
  commentButton: ["commentButton"],
  aiButton: ["aiButton"],
  codeButton: ["codeButton"],
  shareButton: ["shareButton"],
  publishButton: ["publishButton"],
  stop: ["stop"],
  avatar: ["avatar"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  left: "div";
  logoLink: "a";
  titleSegment: "div";
  projectTitle: "div";
  projectMenu: typeof MenuButton;
  saveIndicator: typeof SaveIndicator;
  branchSeparator: "svg";
  branchSegment: "div";
  branchSwitcher: typeof BranchSwitcher;
  arenaSeparator: "svg";
  arenaSegment: "div";
  arenaSwitcher: typeof ArenaSwitcher;
  previewSelect: typeof Select;
  variantsComboSelect: typeof VariantsComboSelect;
  centerLive: "div";
  right: "div";
  freeTrial: typeof FreeTrial;
  viewAsButton: typeof ViewAsButton;
  zoomButton: typeof ZoomButton;
  viewButton: typeof ViewButton;
  play: typeof IconButton;
  livePopOutButton: typeof LivePopOutButton;
  commentButton: typeof CommentButton;
  aiButton: typeof AiButton;
  codeButton: typeof CodeButton;
  shareButton: typeof ShareButton;
  publishButton: typeof PublishButton;
  stop: typeof Button;
  avatar: typeof PlasmicImg__;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicTopBar__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicTopBar__VariantsArgs;
    args?: PlasmicTopBar__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicTopBar__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicTopBar__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicTopBar__ArgProps,
          internalVariantPropNames: PlasmicTopBar__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicTopBar__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicTopBar";
  } else {
    func.displayName = `PlasmicTopBar.${nodeName}`;
  }
  return func;
}

export const PlasmicTopBar = Object.assign(
  // Top-level PlasmicTopBar renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    left: makeNodeComponent("left"),
    logoLink: makeNodeComponent("logoLink"),
    titleSegment: makeNodeComponent("titleSegment"),
    projectTitle: makeNodeComponent("projectTitle"),
    projectMenu: makeNodeComponent("projectMenu"),
    saveIndicator: makeNodeComponent("saveIndicator"),
    branchSeparator: makeNodeComponent("branchSeparator"),
    branchSegment: makeNodeComponent("branchSegment"),
    branchSwitcher: makeNodeComponent("branchSwitcher"),
    arenaSeparator: makeNodeComponent("arenaSeparator"),
    arenaSegment: makeNodeComponent("arenaSegment"),
    arenaSwitcher: makeNodeComponent("arenaSwitcher"),
    previewSelect: makeNodeComponent("previewSelect"),
    variantsComboSelect: makeNodeComponent("variantsComboSelect"),
    centerLive: makeNodeComponent("centerLive"),
    right: makeNodeComponent("right"),
    freeTrial: makeNodeComponent("freeTrial"),
    viewAsButton: makeNodeComponent("viewAsButton"),
    zoomButton: makeNodeComponent("zoomButton"),
    viewButton: makeNodeComponent("viewButton"),
    play: makeNodeComponent("play"),
    livePopOutButton: makeNodeComponent("livePopOutButton"),
    commentButton: makeNodeComponent("commentButton"),
    aiButton: makeNodeComponent("aiButton"),
    codeButton: makeNodeComponent("codeButton"),
    shareButton: makeNodeComponent("shareButton"),
    publishButton: makeNodeComponent("publishButton"),
    stop: makeNodeComponent("stop"),
    avatar: makeNodeComponent("avatar"),

    // Metadata about props expected for PlasmicTopBar
    internalVariantProps: PlasmicTopBar__VariantProps,
    internalArgProps: PlasmicTopBar__ArgProps,
  }
);

export default PlasmicTopBar;
/* prettier-ignore-end */
