/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: 6CrqkTcB6gSAHoA8c8zpNz
// Component: BqSsxlQdj2F0

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import IconButton from "../../components/widgets/IconButton"; // plasmic-import: LPry-TF4j22a/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic_plasmic_kit_top_bar.module.css"; // plasmic-import: 6CrqkTcB6gSAHoA8c8zpNz/projectcss
import sty from "./PlasmicAiButton.module.css"; // plasmic-import: BqSsxlQdj2F0/css

import SparklesSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__SparklesSvg"; // plasmic-import: 9Z0Cu-c5J/icon

createPlasmicElementProxy;

export type PlasmicAiButton__VariantMembers = {
  active: "active";
};
export type PlasmicAiButton__VariantsArgs = {
  active?: SingleBooleanChoiceArg<"active">;
};
type VariantPropType = keyof PlasmicAiButton__VariantsArgs;
export const PlasmicAiButton__VariantProps = new Array<VariantPropType>(
  "active"
);

export type PlasmicAiButton__ArgsType = {};
type ArgPropType = keyof PlasmicAiButton__ArgsType;
export const PlasmicAiButton__ArgProps = new Array<ArgPropType>();

export type PlasmicAiButton__OverridesType = {
  root?: Flex__<typeof IconButton>;
  svg?: Flex__<"svg">;
};

export interface DefaultAiButtonProps {
  active?: SingleBooleanChoiceArg<"active">;
  className?: string;
}

const $$ = {};

function PlasmicAiButton__RenderFunc(props: {
  variants: PlasmicAiButton__VariantsArgs;
  args: PlasmicAiButton__ArgsType;
  overrides: PlasmicAiButton__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "active",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.active,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <IconButton
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames("__wab_instance", sty.root, {
        [sty.rootactive]: hasVariant($state, "active", "active"),
      })}
      size={"medium"}
      type={hasVariant($state, "active", "active") ? ["primary"] : undefined}
      withBackgroundHover={true}
    >
      <SparklesSvgIcon
        data-plasmic-name={"svg"}
        data-plasmic-override={overrides.svg}
        className={classNames(projectcss.all, sty.svg)}
        role={"img"}
      />
    </IconButton>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "svg"],
  svg: ["svg"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof IconButton;
  svg: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicAiButton__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicAiButton__VariantsArgs;
    args?: PlasmicAiButton__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicAiButton__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicAiButton__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicAiButton__ArgProps,
          internalVariantPropNames: PlasmicAiButton__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicAiButton__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicAiButton";
  } else {
    func.displayName = `PlasmicAiButton.${nodeName}`;
  }
  return func;
}

export const PlasmicAiButton = Object.assign(
  // Top-level PlasmicAiButton renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    svg: makeNodeComponent("svg"),

    // Metadata about props expected for PlasmicAiButton
    internalVariantProps: PlasmicAiButton__VariantProps,
    internalArgProps: PlasmicAiButton__ArgProps,
  }
);

export default PlasmicAiButton;
/* prettier-ignore-end */
