.root {
  display: flex;
  width: 100%;
  position: relative;
  padding-right: 0.75rem;
  padding-left: 0rem;
  background: var(--token-iR8SeEwQZ);
  height: 3rem;
  min-width: 0;
  border-bottom: 1px solid var(--token-hoA5qaM-91G);
}
.root > :global(.__wab_flex-container) {
  align-items: center;
  justify-content: center;
  min-width: 0;
  margin-left: calc(0px - 12px);
  width: calc(100% + 12px);
}
.root > :global(.__wab_flex-container) > *,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.root > :global(.__wab_flex-container) > picture > img,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > picture > img {
  margin-left: 12px;
}
.rootplasmicAdminMode {
  background: var(--token-_TR9sJIkSoKq);
}
.left {
  display: flex;
  position: relative;
  flex-direction: row;
  align-self: auto;
  left: auto;
  width: 100%;
  overflow: hidden;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.left > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  min-width: 0;
  min-height: 0;
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.left > :global(.__wab_flex-container) > *,
.left > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.left > :global(.__wab_flex-container) > picture > img,
.left > :global(.__wab_flex-container) > :global(.__wab_slot) > picture > img {
  margin-left: 8px;
}
.freeBox___7MgoJ {
  position: relative;
  display: flex;
  flex-direction: row;
  padding-right: 10px;
  padding-left: 10px;
}
.svg__jQrU5 {
  object-fit: cover;
  width: 40px;
  height: 40px;
  color: var(--token-PTyaboLP9ZK);
}
.titleSegment {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  height: auto;
  overflow: hidden;
}
.projectTitle {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 30vw;
  font-weight: 600;
  cursor: pointer;
  white-space: pre;
  text-overflow: ellipsis;
  overflow: hidden;
  min-width: 0;
}
.projectMenu:global(.__wab_instance):global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.saveIndicator:global(.__wab_instance) {
  position: relative;
}
.branchSeparator {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  color: #e3e3e0;
  height: 100%;
  min-height: 0;
}
.branchSegment {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.branchSwitcher:global(.__wab_instance):global(.__wab_instance) {
  max-width: 100%;
}
.arenaSeparator {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  color: #e3e3e0;
  height: 100%;
  min-height: 0;
}
.arenaSegment {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  height: auto;
}
.arenaSwitcher:global(.__wab_instance):global(.__wab_instance) {
  position: relative;
}
.previewSelect:global(.__wab_instance) {
  position: relative;
}
.text__yJyck {
  width: auto;
}
.option__kUsDc:global(.__wab_instance) {
  position: relative;
}
.option__hrBrh:global(.__wab_instance) {
  position: relative;
}
.optionGroup__ywnYm:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.option__m1Pv:global(.__wab_instance) {
  position: relative;
}
.option__lwpTf:global(.__wab_instance) {
  position: relative;
}
.optionGroup__a7R08:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.option___2Zkx1:global(.__wab_instance) {
  position: relative;
}
.option__smpZp:global(.__wab_instance) {
  position: relative;
}
.option___7Wp8R:global(.__wab_instance) {
  position: relative;
}
.svg__aw2Im {
  position: relative;
  object-fit: cover;
  min-width: 16px;
  min-height: 16px;
  width: 16px;
  height: 16px;
}
.svg__u6BdQ {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  color: #e3e3e0;
  height: 100%;
  min-height: 0;
}
.svgmode_preview__u6BdQqMfAz {
  display: block;
}
.variantsComboSelect:global(.__wab_instance) {
  position: relative;
}
.freeBox__ab66I {
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: auto;
  height: auto;
  max-width: 100%;
  display: none;
  padding: 0px;
}
.freeBoxplasmicAdminMode__ab66IGagJ {
  display: flex;
}
.text__g59UJ {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  white-space: pre;
  min-width: 0;
}
.centerLive {
  display: flex;
  position: relative;
  flex-direction: row;
  height: 2rem;
}
.centerLive > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.centerLive > :global(.__wab_flex-container) > *,
.centerLive > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.centerLive > :global(.__wab_flex-container) > picture > img,
.centerLive
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 8px;
}
.right {
  display: flex;
  position: relative;
  flex-direction: row;
  left: auto;
  right: auto;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.right > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  min-width: 0;
  min-height: 0;
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.right > :global(.__wab_flex-container) > *,
.right > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.right > :global(.__wab_flex-container) > picture > img,
.right > :global(.__wab_flex-container) > :global(.__wab_slot) > picture > img {
  margin-left: 8px;
}
.freeTrial:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.svg__wdih6 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  height: 1em;
  display: none;
}
.svgplasmicAdminMode__wdih6GagJ {
  display: block;
}
.viewAsButton:global(.__wab_instance):global(.__wab_instance) {
  position: relative;
}
.zoomButton:global(.__wab_instance):global(.__wab_instance) {
  position: relative;
}
.viewButton:global(.__wab_instance):global(.__wab_instance) {
  position: relative;
}
.freeBox__wHg9U {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  min-height: 0;
}
.play:global(.__wab_instance) {
  position: relative;
}
.svg__amc5N {
  display: flex;
  position: relative;
  object-fit: cover;
  color: var(--token-GEBK7U4Am62W);
  width: 1.5rem;
  height: 1.5rem;
}
.svg__erE2Y {
  display: flex;
  position: relative;
  object-fit: cover;
  color: var(--token-UunsGa2Y3t3);
  height: 1em;
}
.livePopOutButton:global(.__wab_instance):global(.__wab_instance) {
  width: 32px;
  flex-shrink: 0;
}
.freeBox__tYUhw {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  margin: 0px 10px 0px 20px;
}
.freeBox__tYUhw > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  min-width: 0;
  margin-left: calc(0px - 4px);
  width: calc(100% + 4px);
}
.freeBox__tYUhw > :global(.__wab_flex-container) > *,
.freeBox__tYUhw > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__tYUhw > :global(.__wab_flex-container) > picture > img,
.freeBox__tYUhw
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 4px;
}
.freeBoxmode_preview__tYUhwQMfAz {
  display: none;
}
.aiButton:global(.__wab_instance):global(.__wab_instance) {
  max-width: 100%;
}
.freeBox___3DoVm {
  display: flex;
  position: relative;
}
.freeBox___3DoVm > :global(.__wab_flex-container) {
  align-items: center;
  justify-content: flex-start;
  margin-left: calc(0px - 8px);
  width: calc(100% + 8px);
}
.freeBox___3DoVm > :global(.__wab_flex-container) > *,
.freeBox___3DoVm > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox___3DoVm > :global(.__wab_flex-container) > picture > img,
.freeBox___3DoVm
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: 8px;
}
.shareButton:global(.__wab_instance):global(.__wab_instance) {
  position: relative;
}
.publishButton:global(.__wab_instance) {
  position: relative;
}
.stop:global(.__wab_instance) {
  position: relative;
}
.svg__tF90T {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 16px;
  color: var(--token-iDpByWoW0eF);
}
.svgmode_preview__tF90TQMfAz {
  color: var(--token-UunsGa2Y3t3);
}
.svg__c2B4M {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
.avatar {
  position: relative;
  object-fit: cover;
  height: 1.5rem;
}
.avatar > picture > img {
  object-fit: cover;
}
