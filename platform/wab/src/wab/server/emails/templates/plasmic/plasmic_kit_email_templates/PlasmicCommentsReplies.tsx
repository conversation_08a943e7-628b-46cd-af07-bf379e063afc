/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: I_o0WZFfuhAO

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import {
  EmailHr,
  EmailRow,
  EmailSection,
  EmailText,
} from "@/wab/server/emails/components.tsx"; // plasmic-import: ccI_uNhThjcT/codeComponent
import AtomsButtonOutline from "../../AtomsButtonOutline"; // plasmic-import: 5OdShbT02UoK/component
import AtomsCard from "../../AtomsCard"; // plasmic-import: 2cidd9ockVoM/component
import CommentsComment from "../../CommentsComment"; // plasmic-import: NUaRKxTU3gYx/component
import CommentsHeader from "../../CommentsHeader"; // plasmic-import: aekteDGKbn5u/component

createPlasmicElementProxy;

export type PlasmicCommentsReplies__VariantMembers = {};
export type PlasmicCommentsReplies__VariantsArgs = {};
type VariantPropType = keyof PlasmicCommentsReplies__VariantsArgs;
export const PlasmicCommentsReplies__VariantProps =
  new Array<VariantPropType>();

export type PlasmicCommentsReplies__ArgsType = {
  projectUrl?: string;
  replies?: any;
};
type ArgPropType = keyof PlasmicCommentsReplies__ArgsType;
export const PlasmicCommentsReplies__ArgProps = new Array<ArgPropType>(
  "projectUrl",
  "replies"
);

export type PlasmicCommentsReplies__OverridesType = {
  root?: Flex__<typeof EmailSection>;
  commentsHeader?: Flex__<typeof CommentsHeader>;
  atomsCard?: Flex__<typeof AtomsCard>;
  emailHorizontalRule?: Flex__<typeof EmailHr>;
  emailText?: Flex__<typeof EmailText>;
  atomsButtonOutline?: Flex__<typeof AtomsButtonOutline>;
};

export interface DefaultCommentsRepliesProps {
  projectUrl?: string;
  replies?: any;
  className?: string;
}

const $$ = {};

function PlasmicCommentsReplies__RenderFunc(props: {
  variants: PlasmicCommentsReplies__VariantsArgs;
  args: PlasmicCommentsReplies__ArgsType;
  overrides: PlasmicCommentsReplies__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          projectUrl: "https://plasmic.app",
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <EmailSection
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        "__wab_instance",
        "root_reset_taNK5uwsoPrzfpYmBVwUwX",
        "plasmic_default_styles",
        "plasmic_mixins",
        "plasmic_tokens",
        "CommentsReplies__root__bJzr2"
      )}
      style={{ marginBottom: 32 }}
    >
      <CommentsHeader
        data-plasmic-name={"commentsHeader"}
        data-plasmic-override={overrides.commentsHeader}
        className={classNames(
          "__wab_instance",
          "CommentsReplies__commentsHeader__p29Mc"
        )}
        content={(() => {
          try {
            return (() => {
              const participants = [
                ...new Set(
                  $props.replies
                    .map((r) => r.replies)
                    .flat()
                    .map((r) => r.name)
                ),
              ];
              const singular = participants.length === 1;
              return `${participants[0]}${
                singular ? "" : " and others"
              } replied to ${
                $props.replies.length === 1 ? "a comment" : "these comments"
              }`;
            })();
          } catch (e) {
            if (
              e instanceof TypeError ||
              e?.plasmicType === "PlasmicUndefinedDataError"
            ) {
              return undefined;
            }
            throw e;
          }
        })()}
      />

      {((_par) => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
        (() => {
          try {
            return $props.replies;
          } catch (e) {
            if (
              e instanceof TypeError ||
              e?.plasmicType === "PlasmicUndefinedDataError"
            ) {
              return [];
            }
            throw e;
          }
        })()
      ).map((__plasmic_item_0, __plasmic_idx_0) => {
        const currentItem = __plasmic_item_0;
        const currentIndex = __plasmic_idx_0;
        return (
          <EmailRow
            className={classNames(
              "__wab_instance",
              "CommentsReplies__emailRow__ykOxi"
            )}
            key={currentIndex}
            style={{ marginBottom: 16 }}
          >
            <AtomsCard
              data-plasmic-name={"atomsCard"}
              data-plasmic-override={overrides.atomsCard}
              className={classNames(
                "__wab_instance",
                "CommentsReplies__atomsCard__lyYsb"
              )}
            >
              <CommentsComment
                className={classNames(
                  "__wab_instance",
                  "CommentsReplies__commentsComment__rvEjs"
                )}
                content={(() => {
                  try {
                    return currentItem.rootComment.body;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
                name={(() => {
                  try {
                    return currentItem.rootComment.name;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
              />

              <EmailRow
                className={classNames(
                  "__wab_instance",
                  "CommentsReplies__emailRow__mnhNb"
                )}
                style={{ position: "relative" }}
              >
                <EmailHr
                  data-plasmic-name={"emailHorizontalRule"}
                  data-plasmic-override={overrides.emailHorizontalRule}
                  className={classNames(
                    "__wab_instance",
                    "CommentsReplies__emailHorizontalRule__oTdLb"
                  )}
                  style={{ marginTop: "24px", marginBottom: "0px" }}
                />

                <EmailText
                  data-plasmic-name={"emailText"}
                  data-plasmic-override={overrides.emailText}
                  children={"NEW COMMENTS"}
                  className={classNames(
                    "__wab_instance",
                    "CommentsReplies__emailText__gPkC"
                  )}
                  style={{
                    fontSize: "11px",
                    fontWeight: 600,
                    margin: 0,
                    color: "rgb(213, 211, 211)",
                    textAlign: "center",
                  }}
                />
              </EmailRow>
              {((_par) => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
                (() => {
                  try {
                    return currentItem.replies;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return [];
                    }
                    throw e;
                  }
                })()
              ).map((__plasmic_item_1, __plasmic_idx_1) => {
                const currentItem = __plasmic_item_1;
                const currentIndex = __plasmic_idx_1;
                return (
                  <EmailRow
                    className={classNames(
                      "__wab_instance",
                      "CommentsReplies__emailRow__pURdH"
                    )}
                    key={currentIndex}
                    style={{ margin: "24px 0" }}
                  >
                    <CommentsComment
                      avatarUrl={(() => {
                        try {
                          return currentItem.avatarUrl;
                        } catch (e) {
                          if (
                            e instanceof TypeError ||
                            e?.plasmicType === "PlasmicUndefinedDataError"
                          ) {
                            return {
                              src: "https://img.plasmic.app/img-optimizer/v1/img/f86d5d7ae700c37dd8db36806074f231.png",
                              fullWidth: 600,
                              fullHeight: 600,
                            };
                          }
                          throw e;
                        }
                      })()}
                      className={classNames(
                        "__wab_instance",
                        "CommentsReplies__commentsComment__upQga"
                      )}
                      content={(() => {
                        try {
                          return currentItem.comment;
                        } catch (e) {
                          if (
                            e instanceof TypeError ||
                            e?.plasmicType === "PlasmicUndefinedDataError"
                          ) {
                            return undefined;
                          }
                          throw e;
                        }
                      })()}
                      name={(() => {
                        try {
                          return currentItem.name;
                        } catch (e) {
                          if (
                            e instanceof TypeError ||
                            e?.plasmicType === "PlasmicUndefinedDataError"
                          ) {
                            return undefined;
                          }
                          throw e;
                        }
                      })()}
                    />
                  </EmailRow>
                );
              })}
              <AtomsButtonOutline
                data-plasmic-name={"atomsButtonOutline"}
                data-plasmic-override={overrides.atomsButtonOutline}
                className={classNames(
                  "__wab_instance",
                  "CommentsReplies__atomsButtonOutline__sfuMu"
                )}
                href={args.projectUrl}
                text={"View in Plasmic"}
              />
            </AtomsCard>
          </EmailRow>
        );
      })}
    </EmailSection>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "commentsHeader",
    "atomsCard",
    "emailHorizontalRule",
    "emailText",
    "atomsButtonOutline",
  ],
  commentsHeader: ["commentsHeader"],
  atomsCard: [
    "atomsCard",
    "emailHorizontalRule",
    "emailText",
    "atomsButtonOutline",
  ],
  emailHorizontalRule: ["emailHorizontalRule"],
  emailText: ["emailText"],
  atomsButtonOutline: ["atomsButtonOutline"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof EmailSection;
  commentsHeader: typeof CommentsHeader;
  atomsCard: typeof AtomsCard;
  emailHorizontalRule: typeof EmailHr;
  emailText: typeof EmailText;
  atomsButtonOutline: typeof AtomsButtonOutline;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicCommentsReplies__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicCommentsReplies__VariantsArgs;
    args?: PlasmicCommentsReplies__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicCommentsReplies__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicCommentsReplies__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicCommentsReplies__ArgProps,
          internalVariantPropNames: PlasmicCommentsReplies__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicCommentsReplies__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicCommentsReplies";
  } else {
    func.displayName = `PlasmicCommentsReplies.${nodeName}`;
  }
  return func;
}

export const PlasmicCommentsReplies = Object.assign(
  // Top-level PlasmicCommentsReplies renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    commentsHeader: makeNodeComponent("commentsHeader"),
    atomsCard: makeNodeComponent("atomsCard"),
    emailHorizontalRule: makeNodeComponent("emailHorizontalRule"),
    emailText: makeNodeComponent("emailText"),
    atomsButtonOutline: makeNodeComponent("atomsButtonOutline"),

    // Metadata about props expected for PlasmicCommentsReplies
    internalVariantProps: PlasmicCommentsReplies__VariantProps,
    internalArgProps: PlasmicCommentsReplies__ArgProps,
  }
);

export default PlasmicCommentsReplies;
/* prettier-ignore-end */
