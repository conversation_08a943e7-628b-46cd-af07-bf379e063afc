/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: coNtjSHoDNSq

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  renderPlasmicSlot,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import {
  EmailContainer,
  EmailImage,
  EmailLink,
} from "@/wab/server/emails/components.tsx"; // plasmic-import: oy2tNUrSNuyD/codeComponent
import Footer from "../../Footer"; // plasmic-import: XGWqRhmEjwRw/component

createPlasmicElementProxy;

export type PlasmicLayout__VariantMembers = {};
export type PlasmicLayout__VariantsArgs = {};
type VariantPropType = keyof PlasmicLayout__VariantsArgs;
export const PlasmicLayout__VariantProps = new Array<VariantPropType>();

export type PlasmicLayout__ArgsType = { children?: React.ReactNode };
type ArgPropType = keyof PlasmicLayout__ArgsType;
export const PlasmicLayout__ArgProps = new Array<ArgPropType>("children");

export type PlasmicLayout__OverridesType = {
  root?: Flex__<typeof EmailContainer>;
  emailLink?: Flex__<typeof EmailLink>;
  emailImage?: Flex__<typeof EmailImage>;
  footer?: Flex__<typeof Footer>;
};

export interface DefaultLayoutProps {
  children?: React.ReactNode;
  className?: string;
}

const $$ = {};

function PlasmicLayout__RenderFunc(props: {
  variants: PlasmicLayout__VariantsArgs;
  args: PlasmicLayout__ArgsType;
  overrides: PlasmicLayout__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <EmailContainer
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        "__wab_instance",
        "root_reset_taNK5uwsoPrzfpYmBVwUwX",
        "plasmic_default_styles",
        "plasmic_mixins",
        "plasmic_tokens",
        "Layout__root__ndSek"
      )}
      style={{
        backgroundColor: "#ffffff",
        borderRadius: "8px",
        padding: "24px",
      }}
    >
      <EmailLink
        data-plasmic-name={"emailLink"}
        data-plasmic-override={overrides.emailLink}
        className={classNames("__wab_instance", "Layout__emailLink___23HjL")}
        href={"https://plasmic.app"}
        image={
          <EmailImage
            data-plasmic-name={"emailImage"}
            data-plasmic-override={overrides.emailImage}
            className={classNames(
              "__wab_instance",
              "Layout__emailImage__fhmjg"
            )}
            height={"48"}
            src={
              "https://site-assets.plasmic.app/ef1c5e4024825224b5a8dc6bf0f823d9.png"
            }
          />
        }
        type={"image"}
      />

      {renderPlasmicSlot({
        defaultContents: null,
        value: args.children,
      })}
      <Footer
        data-plasmic-name={"footer"}
        data-plasmic-override={overrides.footer}
        className={classNames("__wab_instance", "Layout__footer___9WflH")}
      />
    </EmailContainer>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "emailLink", "emailImage", "footer"],
  emailLink: ["emailLink", "emailImage"],
  emailImage: ["emailImage"],
  footer: ["footer"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof EmailContainer;
  emailLink: typeof EmailLink;
  emailImage: typeof EmailImage;
  footer: typeof Footer;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicLayout__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicLayout__VariantsArgs;
    args?: PlasmicLayout__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicLayout__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicLayout__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicLayout__ArgProps,
          internalVariantPropNames: PlasmicLayout__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicLayout__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicLayout";
  } else {
    func.displayName = `PlasmicLayout.${nodeName}`;
  }
  return func;
}

export const PlasmicLayout = Object.assign(
  // Top-level PlasmicLayout renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    emailLink: makeNodeComponent("emailLink"),
    emailImage: makeNodeComponent("emailImage"),
    footer: makeNodeComponent("footer"),

    // Metadata about props expected for PlasmicLayout
    internalVariantProps: PlasmicLayout__VariantProps,
    internalArgProps: PlasmicLayout__ArgProps,
  }
);

export default PlasmicLayout;
/* prettier-ignore-end */
