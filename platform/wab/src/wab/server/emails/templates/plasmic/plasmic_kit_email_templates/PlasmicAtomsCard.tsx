/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: 2cidd9ockVoM

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  renderPlasmicSlot,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import { EmailSection, EmailText } from "@/wab/server/emails/components.tsx"; // plasmic-import: ccI_uNhThjcT/codeComponent

createPlasmicElementProxy;

export type PlasmicAtomsCard__VariantMembers = {};
export type PlasmicAtomsCard__VariantsArgs = {};
type VariantPropType = keyof PlasmicAtomsCard__VariantsArgs;
export const PlasmicAtomsCard__VariantProps = new Array<VariantPropType>();

export type PlasmicAtomsCard__ArgsType = { children?: React.ReactNode };
type ArgPropType = keyof PlasmicAtomsCard__ArgsType;
export const PlasmicAtomsCard__ArgProps = new Array<ArgPropType>("children");

export type PlasmicAtomsCard__OverridesType = {
  root?: Flex__<typeof EmailSection>;
};

export interface DefaultAtomsCardProps {
  children?: React.ReactNode;
  className?: string;
}

const $$ = {};

function PlasmicAtomsCard__RenderFunc(props: {
  variants: PlasmicAtomsCard__VariantsArgs;
  args: PlasmicAtomsCard__ArgsType;
  overrides: PlasmicAtomsCard__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <EmailSection
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        "__wab_instance",
        "root_reset_taNK5uwsoPrzfpYmBVwUwX",
        "plasmic_default_styles",
        "plasmic_mixins",
        "plasmic_tokens",
        "AtomsCard__root__v5KzG"
      )}
      style={{ borderRadius: 18, padding: 24, border: "1px solid #00002F26" }}
    >
      {renderPlasmicSlot({
        defaultContents: (
          <EmailText
            children={"Card content"}
            className={classNames(
              "__wab_instance",
              "AtomsCard__emailText__k2SEp"
            )}
          />
        ),

        value: args.children,
      })}
    </EmailSection>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof EmailSection;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicAtomsCard__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicAtomsCard__VariantsArgs;
    args?: PlasmicAtomsCard__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicAtomsCard__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicAtomsCard__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicAtomsCard__ArgProps,
          internalVariantPropNames: PlasmicAtomsCard__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicAtomsCard__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicAtomsCard";
  } else {
    func.displayName = `PlasmicAtomsCard.${nodeName}`;
  }
  return func;
}

export const PlasmicAtomsCard = Object.assign(
  // Top-level PlasmicAtomsCard renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicAtomsCard
    internalVariantProps: PlasmicAtomsCard__VariantProps,
    internalArgProps: PlasmicAtomsCard__ArgProps,
  }
);

export default PlasmicAtomsCard;
/* prettier-ignore-end */
