/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: t8pedfUoDPO4

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import { EmailRow, EmailSection } from "@/wab/server/emails/components.tsx"; // plasmic-import: ccI_uNhThjcT/codeComponent
import AtomsButtonOutline from "../../AtomsButtonOutline"; // plasmic-import: 5OdShbT02UoK/component
import AtomsCard from "../../AtomsCard"; // plasmic-import: 2cidd9ockVoM/component
import CommentsComment from "../../CommentsComment"; // plasmic-import: NUaRKxTU3gYx/component
import CommentsHeader from "../../CommentsHeader"; // plasmic-import: aekteDGKbn5u/component

createPlasmicElementProxy;

export type PlasmicCommentsComments__VariantMembers = {};
export type PlasmicCommentsComments__VariantsArgs = {};
type VariantPropType = keyof PlasmicCommentsComments__VariantsArgs;
export const PlasmicCommentsComments__VariantProps =
  new Array<VariantPropType>();

export type PlasmicCommentsComments__ArgsType = {
  projectUrl?: string;
  comments?: any;
};
type ArgPropType = keyof PlasmicCommentsComments__ArgsType;
export const PlasmicCommentsComments__ArgProps = new Array<ArgPropType>(
  "projectUrl",
  "comments"
);

export type PlasmicCommentsComments__OverridesType = {
  root?: Flex__<typeof EmailSection>;
  commentsHeader?: Flex__<typeof CommentsHeader>;
  emailRow?: Flex__<typeof EmailRow>;
  atomsCard?: Flex__<typeof AtomsCard>;
  commentsComment?: Flex__<typeof CommentsComment>;
  atomsButtonOutline?: Flex__<typeof AtomsButtonOutline>;
};

export interface DefaultCommentsCommentsProps {
  projectUrl?: string;
  comments?: any;
  className?: string;
}

const $$ = {};

function PlasmicCommentsComments__RenderFunc(props: {
  variants: PlasmicCommentsComments__VariantsArgs;
  args: PlasmicCommentsComments__ArgsType;
  overrides: PlasmicCommentsComments__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          projectUrl: "https://plasmic.app",
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <EmailSection
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        "__wab_instance",
        "root_reset_taNK5uwsoPrzfpYmBVwUwX",
        "plasmic_default_styles",
        "plasmic_mixins",
        "plasmic_tokens",
        "CommentsComments__root__b99VU"
      )}
      style={{ marginBottom: 48 }}
    >
      <CommentsHeader
        data-plasmic-name={"commentsHeader"}
        data-plasmic-override={overrides.commentsHeader}
        className={classNames(
          "__wab_instance",
          "CommentsComments__commentsHeader___96ErN"
        )}
        content={(() => {
          try {
            return (() => {
              const singular =
                [...new Set($props.comments.map((c) => c.name))].length === 1;
              return `${$props.comments[0]?.name}${
                singular ? "" : " and others"
              } left ${
                $props.comments.length === 1
                  ? "a comment"
                  : $props.comments.length + " comments"
              }`;
            })();
          } catch (e) {
            if (
              e instanceof TypeError ||
              e?.plasmicType === "PlasmicUndefinedDataError"
            ) {
              return undefined;
            }
            throw e;
          }
        })()}
      />

      {((_par) => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
        (() => {
          try {
            return $props.comments;
          } catch (e) {
            if (
              e instanceof TypeError ||
              e?.plasmicType === "PlasmicUndefinedDataError"
            ) {
              return [];
            }
            throw e;
          }
        })()
      ).map((__plasmic_item_0, __plasmic_idx_0) => {
        const currentItem = __plasmic_item_0;
        const currentIndex = __plasmic_idx_0;
        return (
          <EmailRow
            data-plasmic-name={"emailRow"}
            data-plasmic-override={overrides.emailRow}
            className={classNames(
              "__wab_instance",
              "CommentsComments__emailRow__tfhyf"
            )}
            key={currentIndex}
            style={{ marginBottom: 16 }}
          >
            <AtomsCard
              data-plasmic-name={"atomsCard"}
              data-plasmic-override={overrides.atomsCard}
              className={classNames(
                "__wab_instance",
                "CommentsComments__atomsCard__bwIeb"
              )}
            >
              <CommentsComment
                data-plasmic-name={"commentsComment"}
                data-plasmic-override={overrides.commentsComment}
                avatarUrl={(() => {
                  try {
                    return currentItem.avatarUrl;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
                className={classNames(
                  "__wab_instance",
                  "CommentsComments__commentsComment__fzGU"
                )}
                content={(() => {
                  try {
                    return currentItem.comment;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
                name={(() => {
                  try {
                    return currentItem.name;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
              />
            </AtomsCard>
          </EmailRow>
        );
      })}
      <AtomsButtonOutline
        data-plasmic-name={"atomsButtonOutline"}
        data-plasmic-override={overrides.atomsButtonOutline}
        className={classNames(
          "__wab_instance",
          "CommentsComments__atomsButtonOutline__xjUkq"
        )}
        href={args.projectUrl}
        text={"View in Plasmic"}
      />
    </EmailSection>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "commentsHeader",
    "emailRow",
    "atomsCard",
    "commentsComment",
    "atomsButtonOutline",
  ],
  commentsHeader: ["commentsHeader"],
  emailRow: ["emailRow", "atomsCard", "commentsComment"],
  atomsCard: ["atomsCard", "commentsComment"],
  commentsComment: ["commentsComment"],
  atomsButtonOutline: ["atomsButtonOutline"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof EmailSection;
  commentsHeader: typeof CommentsHeader;
  emailRow: typeof EmailRow;
  atomsCard: typeof AtomsCard;
  commentsComment: typeof CommentsComment;
  atomsButtonOutline: typeof AtomsButtonOutline;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicCommentsComments__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicCommentsComments__VariantsArgs;
    args?: PlasmicCommentsComments__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicCommentsComments__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicCommentsComments__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicCommentsComments__ArgProps,
          internalVariantPropNames: PlasmicCommentsComments__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicCommentsComments__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicCommentsComments";
  } else {
    func.displayName = `PlasmicCommentsComments.${nodeName}`;
  }
  return func;
}

export const PlasmicCommentsComments = Object.assign(
  // Top-level PlasmicCommentsComments renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    commentsHeader: makeNodeComponent("commentsHeader"),
    emailRow: makeNodeComponent("emailRow"),
    atomsCard: makeNodeComponent("atomsCard"),
    commentsComment: makeNodeComponent("commentsComment"),
    atomsButtonOutline: makeNodeComponent("atomsButtonOutline"),

    // Metadata about props expected for PlasmicCommentsComments
    internalVariantProps: PlasmicCommentsComments__VariantProps,
    internalArgProps: PlasmicCommentsComments__ArgProps,
  }
);

export default PlasmicCommentsComments;
/* prettier-ignore-end */
