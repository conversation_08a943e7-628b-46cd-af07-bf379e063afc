/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: 5OdShbT02UoK

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import { EmailButton } from "@/wab/server/emails/components.tsx"; // plasmic-import: ni9IJwUvaPYp/codeComponent

createPlasmicElementProxy;

export type PlasmicAtomsButtonOutline__VariantMembers = {};
export type PlasmicAtomsButtonOutline__VariantsArgs = {};
type VariantPropType = keyof PlasmicAtomsButtonOutline__VariantsArgs;
export const PlasmicAtomsButtonOutline__VariantProps =
  new Array<VariantPropType>();

export type PlasmicAtomsButtonOutline__ArgsType = {
  text?: string;
  href?: string;
};
type ArgPropType = keyof PlasmicAtomsButtonOutline__ArgsType;
export const PlasmicAtomsButtonOutline__ArgProps = new Array<ArgPropType>(
  "text",
  "href"
);

export type PlasmicAtomsButtonOutline__OverridesType = {
  root?: Flex__<typeof EmailButton>;
};

export interface DefaultAtomsButtonOutlineProps {
  text?: string;
  href?: string;
  className?: string;
}

const $$ = {};

function PlasmicAtomsButtonOutline__RenderFunc(props: {
  variants: PlasmicAtomsButtonOutline__VariantsArgs;
  args: PlasmicAtomsButtonOutline__ArgsType;
  overrides: PlasmicAtomsButtonOutline__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          text: "Button",
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <EmailButton
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      children={args.text}
      className={classNames(
        "__wab_instance",
        "root_reset_taNK5uwsoPrzfpYmBVwUwX",
        "plasmic_default_styles",
        "plasmic_mixins",
        "plasmic_tokens",
        "AtomsButtonOutline__root___0QqL1"
      )}
      href={args.href}
      style={{
        color: "#006DCBF2",
        width: "100%",
        borderRadius: 8,
        padding: 10,
        textAlign: "center",
        border: "1px solid #0084E6A1",
      }}
    />
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof EmailButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicAtomsButtonOutline__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicAtomsButtonOutline__VariantsArgs;
    args?: PlasmicAtomsButtonOutline__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicAtomsButtonOutline__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicAtomsButtonOutline__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicAtomsButtonOutline__ArgProps,
          internalVariantPropNames: PlasmicAtomsButtonOutline__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicAtomsButtonOutline__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicAtomsButtonOutline";
  } else {
    func.displayName = `PlasmicAtomsButtonOutline.${nodeName}`;
  }
  return func;
}

export const PlasmicAtomsButtonOutline = Object.assign(
  // Top-level PlasmicAtomsButtonOutline renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicAtomsButtonOutline
    internalVariantProps: PlasmicAtomsButtonOutline__VariantProps,
    internalArgProps: PlasmicAtomsButtonOutline__ArgProps,
  }
);

export default PlasmicAtomsButtonOutline;
/* prettier-ignore-end */
