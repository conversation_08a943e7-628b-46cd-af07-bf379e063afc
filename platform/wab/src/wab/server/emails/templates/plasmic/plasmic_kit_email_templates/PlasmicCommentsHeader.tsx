/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: aekteDGKbn5u

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import { EmailText } from "@/wab/server/emails/components.tsx"; // plasmic-import: 7eMX-ae3z_Os/codeComponent

createPlasmicElementProxy;

export type PlasmicCommentsHeader__VariantMembers = {};
export type PlasmicCommentsHeader__VariantsArgs = {};
type VariantPropType = keyof PlasmicCommentsHeader__VariantsArgs;
export const PlasmicCommentsHeader__VariantProps = new Array<VariantPropType>();

export type PlasmicCommentsHeader__ArgsType = { content?: string };
type ArgPropType = keyof PlasmicCommentsHeader__ArgsType;
export const PlasmicCommentsHeader__ArgProps = new Array<ArgPropType>(
  "content"
);

export type PlasmicCommentsHeader__OverridesType = {
  root?: Flex__<typeof EmailText>;
};

export interface DefaultCommentsHeaderProps {
  content?: string;
  className?: string;
}

const $$ = {};

function PlasmicCommentsHeader__RenderFunc(props: {
  variants: PlasmicCommentsHeader__VariantsArgs;
  args: PlasmicCommentsHeader__ArgsType;
  overrides: PlasmicCommentsHeader__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          content: ``,
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <EmailText
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      children={args.content}
      className={classNames(
        "__wab_instance",
        "root_reset_taNK5uwsoPrzfpYmBVwUwX",
        "plasmic_default_styles",
        "plasmic_mixins",
        "plasmic_tokens",
        "CommentsHeader__root__v1QrB"
      )}
      style={{
        fontWeight: 500,
        color: "#1C2024",
        fontSize: 16,
        marginTop: 0,
        marginBottom: 32,
      }}
    />
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof EmailText;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicCommentsHeader__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicCommentsHeader__VariantsArgs;
    args?: PlasmicCommentsHeader__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicCommentsHeader__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicCommentsHeader__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicCommentsHeader__ArgProps,
          internalVariantPropNames: PlasmicCommentsHeader__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicCommentsHeader__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicCommentsHeader";
  } else {
    func.displayName = `PlasmicCommentsHeader.${nodeName}`;
  }
  return func;
}

export const PlasmicCommentsHeader = Object.assign(
  // Top-level PlasmicCommentsHeader renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicCommentsHeader
    internalVariantProps: PlasmicCommentsHeader__VariantProps,
    internalArgProps: PlasmicCommentsHeader__ArgProps,
  }
);

export default PlasmicCommentsHeader;
/* prettier-ignore-end */
