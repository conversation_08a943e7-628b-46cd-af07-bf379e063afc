/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: 1YzUicnY5mUG

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import { EmailHeading, EmailText } from "@/wab/server/emails/components.tsx"; // plasmic-import: 0uJtDjGRiPZA/codeComponent
import Layout from "../../Layout"; // plasmic-import: coNtjSHoDNSq/component

createPlasmicElementProxy;

export type PlasmicTemplateTestEmail__VariantMembers = {};
export type PlasmicTemplateTestEmail__VariantsArgs = {};
type VariantPropType = keyof PlasmicTemplateTestEmail__VariantsArgs;
export const PlasmicTemplateTestEmail__VariantProps =
  new Array<VariantPropType>();

export type PlasmicTemplateTestEmail__ArgsType = {
  title?: string;
  content?: string;
};
type ArgPropType = keyof PlasmicTemplateTestEmail__ArgsType;
export const PlasmicTemplateTestEmail__ArgProps = new Array<ArgPropType>(
  "title",
  "content"
);

export type PlasmicTemplateTestEmail__OverridesType = {
  root?: Flex__<typeof Layout>;
  emailHeading?: Flex__<typeof EmailHeading>;
};

export interface DefaultTemplateTestEmailProps {
  title?: string;
  content?: string;
  className?: string;
}

const $$ = {};

function PlasmicTemplateTestEmail__RenderFunc(props: {
  variants: PlasmicTemplateTestEmail__VariantsArgs;
  args: PlasmicTemplateTestEmail__ArgsType;
  overrides: PlasmicTemplateTestEmail__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <Layout
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames("__wab_instance", "TemplateTestEmail__root__jg0Wt")}
    >
      <EmailHeading
        data-plasmic-name={"emailHeading"}
        data-plasmic-override={overrides.emailHeading}
        children={(() => {
          try {
            return $props.title;
          } catch (e) {
            if (
              e instanceof TypeError ||
              e?.plasmicType === "PlasmicUndefinedDataError"
            ) {
              return undefined;
            }
            throw e;
          }
        })()}
        className={classNames(
          "__wab_instance",
          "TemplateTestEmail__emailHeading__mk3Qb"
        )}
      />

      <EmailText
        children={"Hello, world!"}
        className={classNames(
          "__wab_instance",
          "TemplateTestEmail__emailText__d56R8"
        )}
      />

      <EmailText
        children={(() => {
          try {
            return `Dynamic content: "${$props.content}"`;
          } catch (e) {
            if (
              e instanceof TypeError ||
              e?.plasmicType === "PlasmicUndefinedDataError"
            ) {
              return undefined;
            }
            throw e;
          }
        })()}
        className={classNames(
          "__wab_instance",
          "TemplateTestEmail__emailText__uFoMu"
        )}
      />
    </Layout>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "emailHeading"],
  emailHeading: ["emailHeading"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof Layout;
  emailHeading: typeof EmailHeading;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicTemplateTestEmail__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicTemplateTestEmail__VariantsArgs;
    args?: PlasmicTemplateTestEmail__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicTemplateTestEmail__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicTemplateTestEmail__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicTemplateTestEmail__ArgProps,
          internalVariantPropNames: PlasmicTemplateTestEmail__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicTemplateTestEmail__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicTemplateTestEmail";
  } else {
    func.displayName = `PlasmicTemplateTestEmail.${nodeName}`;
  }
  return func;
}

export const PlasmicTemplateTestEmail = Object.assign(
  // Top-level PlasmicTemplateTestEmail renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    emailHeading: makeNodeComponent("emailHeading"),

    // Metadata about props expected for PlasmicTemplateTestEmail
    internalVariantProps: PlasmicTemplateTestEmail__VariantProps,
    internalArgProps: PlasmicTemplateTestEmail__ArgProps,
  }
);

export default PlasmicTemplateTestEmail;
/* prettier-ignore-end */
