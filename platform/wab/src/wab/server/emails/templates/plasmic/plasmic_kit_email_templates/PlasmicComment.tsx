/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: NUaRKxTU3gYx

import * as React from "react";

import {
  Flex as Flex__,
  PlasmicImg as PlasmicImg__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import {
  EmailColumn,
  EmailImage,
  EmailRow,
  EmailText,
} from "@/wab/server/emails/components.tsx"; // plasmic-import: xWkqfjbn8oyX/codeComponent
import AtomsCard from "../../AtomsCard"; // plasmic-import: 2cidd9ockVoM/component

createPlasmicElementProxy;

export type PlasmicComment__VariantMembers = {};
export type PlasmicComment__VariantsArgs = {};
type VariantPropType = keyof PlasmicComment__VariantsArgs;
export const PlasmicComment__VariantProps = new Array<VariantPropType>();

export type PlasmicComment__ArgsType = {
  avatarUrl?: React.ComponentProps<typeof PlasmicImg__>["src"];
  name?: string;
  content?: string;
};
type ArgPropType = keyof PlasmicComment__ArgsType;
export const PlasmicComment__ArgProps = new Array<ArgPropType>(
  "avatarUrl",
  "name",
  "content"
);

export type PlasmicComment__OverridesType = {
  root?: Flex__<typeof AtomsCard>;
  emailRow?: Flex__<typeof EmailRow>;
  emailImage?: Flex__<typeof EmailImage>;
};

export interface DefaultCommentProps {
  avatarUrl?: React.ComponentProps<typeof PlasmicImg__>["src"];
  name?: string;
  content?: string;
}

const $$ = {};

function PlasmicComment__RenderFunc(props: {
  variants: PlasmicComment__VariantsArgs;
  args: PlasmicComment__ArgsType;
  overrides: PlasmicComment__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          avatarUrl: {
            src: "https://img.plasmic.app/img-optimizer/v1/img/05b39e0beb3685bb17ec0de6e58d14b2.png",
            fullWidth: 48,
            fullHeight: 48,
          },
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <AtomsCard
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames("__wab_instance", "Comment__root___7Z90R")}
    >
      <EmailRow
        data-plasmic-name={"emailRow"}
        data-plasmic-override={overrides.emailRow}
      >
        <EmailColumn align={"left"} style={{ width: 56 }} valign={"top"}>
          <EmailImage
            data-plasmic-name={"emailImage"}
            data-plasmic-override={overrides.emailImage}
            src={(() => {
              try {
                return $props.avatarUrl.src;
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return undefined;
                }
                throw e;
              }
            })()}
            width={"48"}
          />
        </EmailColumn>
        <EmailColumn valign={"top"}>
          <EmailText
            children={args.name}
            style={{ fontWeight: 500, margin: 0 }}
          />

          <EmailText children={args.content} style={{ margin: 8 }} />
        </EmailColumn>
      </EmailRow>
    </AtomsCard>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "emailRow", "emailImage"],
  emailRow: ["emailRow", "emailImage"],
  emailImage: ["emailImage"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof AtomsCard;
  emailRow: typeof EmailRow;
  emailImage: typeof EmailImage;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicComment__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicComment__VariantsArgs;
    args?: PlasmicComment__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicComment__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicComment__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicComment__ArgProps,
          internalVariantPropNames: PlasmicComment__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicComment__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicComment";
  } else {
    func.displayName = `PlasmicComment.${nodeName}`;
  }
  return func;
}

export const PlasmicComment = Object.assign(
  // Top-level PlasmicComment renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    emailRow: makeNodeComponent("emailRow"),
    emailImage: makeNodeComponent("emailImage"),

    // Metadata about props expected for PlasmicComment
    internalVariantProps: PlasmicComment__VariantProps,
    internalArgProps: PlasmicComment__ArgProps,
  }
);

export default PlasmicComment;
/* prettier-ignore-end */
