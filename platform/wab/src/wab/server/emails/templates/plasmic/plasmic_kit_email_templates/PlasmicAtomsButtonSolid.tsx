/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: 5PPvgjCI8WyM

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import { EmailButton } from "@/wab/server/emails/components.tsx"; // plasmic-import: ni9IJwUvaPYp/codeComponent

createPlasmicElementProxy;

export type PlasmicAtomsButtonSolid__VariantMembers = {};
export type PlasmicAtomsButtonSolid__VariantsArgs = {};
type VariantPropType = keyof PlasmicAtomsButtonSolid__VariantsArgs;
export const PlasmicAtomsButtonSolid__VariantProps =
  new Array<VariantPropType>();

export type PlasmicAtomsButtonSolid__ArgsType = {
  text?: string;
  href?: string;
};
type ArgPropType = keyof PlasmicAtomsButtonSolid__ArgsType;
export const PlasmicAtomsButtonSolid__ArgProps = new Array<ArgPropType>(
  "text",
  "href"
);

export type PlasmicAtomsButtonSolid__OverridesType = {
  root?: Flex__<typeof EmailButton>;
};

export interface DefaultAtomsButtonSolidProps {
  text?: string;
  href?: string;
  className?: string;
}

const $$ = {};

function PlasmicAtomsButtonSolid__RenderFunc(props: {
  variants: PlasmicAtomsButtonSolid__VariantsArgs;
  args: PlasmicAtomsButtonSolid__ArgsType;
  overrides: PlasmicAtomsButtonSolid__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          text: "Button",
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <EmailButton
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      children={args.text}
      className={classNames(
        "__wab_instance",
        "root_reset_taNK5uwsoPrzfpYmBVwUwX",
        "plasmic_default_styles",
        "plasmic_mixins",
        "plasmic_tokens",
        "AtomsButtonSolid__root___9Zrbp"
      )}
      href={args.href}
      style={{
        backgroundColor: "#0090FF",
        color: "white",
        width: "100%",
        borderRadius: 8,
        padding: 10,
        textAlign: "center",
        border: "1px solid #0090FF",
      }}
    />
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof EmailButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicAtomsButtonSolid__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicAtomsButtonSolid__VariantsArgs;
    args?: PlasmicAtomsButtonSolid__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicAtomsButtonSolid__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicAtomsButtonSolid__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicAtomsButtonSolid__ArgProps,
          internalVariantPropNames: PlasmicAtomsButtonSolid__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicAtomsButtonSolid__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicAtomsButtonSolid";
  } else {
    func.displayName = `PlasmicAtomsButtonSolid.${nodeName}`;
  }
  return func;
}

export const PlasmicAtomsButtonSolid = Object.assign(
  // Top-level PlasmicAtomsButtonSolid renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicAtomsButtonSolid
    internalVariantProps: PlasmicAtomsButtonSolid__VariantProps,
    internalArgProps: PlasmicAtomsButtonSolid__ArgProps,
  }
);

export default PlasmicAtomsButtonSolid;
/* prettier-ignore-end */
