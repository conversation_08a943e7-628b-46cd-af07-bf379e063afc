/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: dNwSNaiJfR5y

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import {
  EmailLink,
  EmailMarkdown,
  EmailRow,
} from "@/wab/server/emails/components.tsx"; // plasmic-import: 7zgaUp0DUGtR/codeComponent
import AtomsButtonOutline from "../../AtomsButtonOutline"; // plasmic-import: 5OdShbT02UoK/component
import CommentsComments from "../../CommentsComments"; // plasmic-import: t8pedfUoDPO4/component
import CommentsMentions from "../../CommentsMentions"; // plasmic-import: xmHJRn1lA9BZ/component
import CommentsReactions from "../../CommentsReactions"; // plasmic-import: akS-yd3gJU3j/component
import CommentsReplies from "../../CommentsReplies"; // plasmic-import: I_o0WZFfuhAO/component
import CommentsResolutions from "../../CommentsResolutions"; // plasmic-import: rUeSkh2_5k3b/component
import Layout from "../../Layout"; // plasmic-import: coNtjSHoDNSq/component

createPlasmicElementProxy;

export type PlasmicTemplateComments__VariantMembers = {};
export type PlasmicTemplateComments__VariantsArgs = {};
type VariantPropType = keyof PlasmicTemplateComments__VariantsArgs;
export const PlasmicTemplateComments__VariantProps =
  new Array<VariantPropType>();

export type PlasmicTemplateComments__ArgsType = {
  projectName?: string;
  projectUrl?: string;
  userName?: string;
  comments?: any;
  replies?: any;
  mentions?: any;
  reactions?: any;
  resolutions?: any;
};
type ArgPropType = keyof PlasmicTemplateComments__ArgsType;
export const PlasmicTemplateComments__ArgProps = new Array<ArgPropType>(
  "projectName",
  "projectUrl",
  "userName",
  "comments",
  "replies",
  "mentions",
  "reactions",
  "resolutions"
);

export type PlasmicTemplateComments__OverridesType = {
  root?: Flex__<typeof Layout>;
  emailLink?: Flex__<typeof EmailLink>;
  commentsMentions?: Flex__<typeof CommentsMentions>;
  commentsReplies?: Flex__<typeof CommentsReplies>;
  commentsComments?: Flex__<typeof CommentsComments>;
  commentsReactions?: Flex__<typeof CommentsReactions>;
  atomsButtonOutline?: Flex__<typeof AtomsButtonOutline>;
  commentsResolutions?: Flex__<typeof CommentsResolutions>;
  emailMarkdown?: Flex__<typeof EmailMarkdown>;
};

export interface DefaultTemplateCommentsProps {
  projectName?: string;
  projectUrl?: string;
  userName?: string;
  comments?: any;
  replies?: any;
  mentions?: any;
  reactions?: any;
  resolutions?: any;
  className?: string;
}

const $$ = {};

function PlasmicTemplateComments__RenderFunc(props: {
  variants: PlasmicTemplateComments__VariantsArgs;
  args: PlasmicTemplateComments__ArgsType;
  overrides: PlasmicTemplateComments__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          projectName: "Project name",
          projectUrl: "https://plasmic.app",
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <Layout
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames("__wab_instance", "TemplateComments__root__wljSi")}
    >
      <EmailLink
        data-plasmic-name={"emailLink"}
        data-plasmic-override={overrides.emailLink}
        className={classNames(
          "__wab_instance",
          "TemplateComments__emailLink__sLzLo"
        )}
        href={args.projectUrl}
        style={{ fontSize: 11, color: "#000000", marginBottom: 0 }}
        text={args.projectName}
        type={"text"}
      />

      {(() => {
        try {
          return $props.mentions.length > 0;
        } catch (e) {
          if (
            e instanceof TypeError ||
            e?.plasmicType === "PlasmicUndefinedDataError"
          ) {
            return true;
          }
          throw e;
        }
      })() ? (
        <EmailRow
          className={classNames(
            "__wab_instance",
            "TemplateComments__emailRow__s32TP"
          )}
        >
          <CommentsMentions
            data-plasmic-name={"commentsMentions"}
            data-plasmic-override={overrides.commentsMentions}
            className={classNames(
              "__wab_instance",
              "TemplateComments__commentsMentions__fFdv5"
            )}
            comments={args.mentions}
            projectUrl={args.projectUrl}
          />
        </EmailRow>
      ) : null}
      {(() => {
        try {
          return $props.replies.length > 0;
        } catch (e) {
          if (
            e instanceof TypeError ||
            e?.plasmicType === "PlasmicUndefinedDataError"
          ) {
            return true;
          }
          throw e;
        }
      })() ? (
        <EmailRow
          className={classNames(
            "__wab_instance",
            "TemplateComments__emailRow__fUirH"
          )}
        >
          <CommentsReplies
            data-plasmic-name={"commentsReplies"}
            data-plasmic-override={overrides.commentsReplies}
            className={classNames(
              "__wab_instance",
              "TemplateComments__commentsReplies__q3Mwu"
            )}
            projectUrl={args.projectUrl}
            replies={args.replies}
          />
        </EmailRow>
      ) : null}
      {(() => {
        try {
          return $props.comments.length > 0;
        } catch (e) {
          if (
            e instanceof TypeError ||
            e?.plasmicType === "PlasmicUndefinedDataError"
          ) {
            return true;
          }
          throw e;
        }
      })() ? (
        <EmailRow
          className={classNames(
            "__wab_instance",
            "TemplateComments__emailRow__i0EcI"
          )}
        >
          <CommentsComments
            data-plasmic-name={"commentsComments"}
            data-plasmic-override={overrides.commentsComments}
            className={classNames(
              "__wab_instance",
              "TemplateComments__commentsComments__hfa6W"
            )}
            comments={args.comments}
            projectUrl={true ? args.projectUrl : args.projectUrl}
          />
        </EmailRow>
      ) : null}
      {(() => {
        try {
          return $props.reactions.length > 0;
        } catch (e) {
          if (
            e instanceof TypeError ||
            e?.plasmicType === "PlasmicUndefinedDataError"
          ) {
            return true;
          }
          throw e;
        }
      })() ? (
        <EmailRow
          className={classNames(
            "__wab_instance",
            "TemplateComments__emailRow__bw2Ab"
          )}
          style={{ marginBottom: 48 }}
        >
          {((_par) => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
            (() => {
              try {
                return $props.reactions;
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return [];
                }
                throw e;
              }
            })()
          ).map((__plasmic_item_0, __plasmic_idx_0) => {
            const currentItem = __plasmic_item_0;
            const currentIndex = __plasmic_idx_0;
            return (
              <CommentsReactions
                data-plasmic-name={"commentsReactions"}
                data-plasmic-override={overrides.commentsReactions}
                className={classNames(
                  "__wab_instance",
                  "TemplateComments__commentsReactions__oiPwI"
                )}
                comment={(() => {
                  try {
                    return currentItem.comment;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
                key={currentIndex}
                name={args.userName}
                projectUrl={args.projectUrl}
                reactions={(() => {
                  try {
                    return currentItem.reactions;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
              />
            );
          })}
          <AtomsButtonOutline
            data-plasmic-name={"atomsButtonOutline"}
            data-plasmic-override={overrides.atomsButtonOutline}
            className={classNames(
              "__wab_instance",
              "TemplateComments__atomsButtonOutline__qG7FQ"
            )}
            href={args.projectUrl}
            text={"View in Plasmic"}
          />
        </EmailRow>
      ) : null}
      {(() => {
        try {
          return $props.resolutions.length > 0;
        } catch (e) {
          if (
            e instanceof TypeError ||
            e?.plasmicType === "PlasmicUndefinedDataError"
          ) {
            return true;
          }
          throw e;
        }
      })() ? (
        <EmailRow
          className={classNames(
            "__wab_instance",
            "TemplateComments__emailRow__bAn9N"
          )}
        >
          <CommentsResolutions
            data-plasmic-name={"commentsResolutions"}
            data-plasmic-override={overrides.commentsResolutions}
            className={classNames(
              "__wab_instance",
              "TemplateComments__commentsResolutions__gQo1M"
            )}
            projectUrl={args.projectUrl}
            resolutions={args.resolutions}
          />
        </EmailRow>
      ) : null}
      <EmailRow
        className={classNames(
          "__wab_instance",
          "TemplateComments__emailRow__kaApI"
        )}
        style={{ marginTop: -16 }}
      >
        <EmailMarkdown
          data-plasmic-name={"emailMarkdown"}
          data-plasmic-override={overrides.emailMarkdown}
          children={(() => {
            try {
              return `You're receiving this email because you have notifications enabled for this project. Manage your project notification settings [here](${$props.projectUrl}).`;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          className={classNames(
            "__wab_instance",
            "TemplateComments__emailMarkdown__w4XaI"
          )}
          markdownContainerStyles={{ whiteSpace: "normal" }}
          markdownCustomStyles={{ p: { fontSize: 11, textAlign: "center" } }}
        />
      </EmailRow>
    </Layout>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "emailLink",
    "commentsMentions",
    "commentsReplies",
    "commentsComments",
    "commentsReactions",
    "atomsButtonOutline",
    "commentsResolutions",
    "emailMarkdown",
  ],
  emailLink: ["emailLink"],
  commentsMentions: ["commentsMentions"],
  commentsReplies: ["commentsReplies"],
  commentsComments: ["commentsComments"],
  commentsReactions: ["commentsReactions"],
  atomsButtonOutline: ["atomsButtonOutline"],
  commentsResolutions: ["commentsResolutions"],
  emailMarkdown: ["emailMarkdown"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof Layout;
  emailLink: typeof EmailLink;
  commentsMentions: typeof CommentsMentions;
  commentsReplies: typeof CommentsReplies;
  commentsComments: typeof CommentsComments;
  commentsReactions: typeof CommentsReactions;
  atomsButtonOutline: typeof AtomsButtonOutline;
  commentsResolutions: typeof CommentsResolutions;
  emailMarkdown: typeof EmailMarkdown;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicTemplateComments__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicTemplateComments__VariantsArgs;
    args?: PlasmicTemplateComments__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicTemplateComments__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicTemplateComments__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicTemplateComments__ArgProps,
          internalVariantPropNames: PlasmicTemplateComments__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicTemplateComments__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicTemplateComments";
  } else {
    func.displayName = `PlasmicTemplateComments.${nodeName}`;
  }
  return func;
}

export const PlasmicTemplateComments = Object.assign(
  // Top-level PlasmicTemplateComments renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    emailLink: makeNodeComponent("emailLink"),
    commentsMentions: makeNodeComponent("commentsMentions"),
    commentsReplies: makeNodeComponent("commentsReplies"),
    commentsComments: makeNodeComponent("commentsComments"),
    commentsReactions: makeNodeComponent("commentsReactions"),
    atomsButtonOutline: makeNodeComponent("atomsButtonOutline"),
    commentsResolutions: makeNodeComponent("commentsResolutions"),
    emailMarkdown: makeNodeComponent("emailMarkdown"),

    // Metadata about props expected for PlasmicTemplateComments
    internalVariantProps: PlasmicTemplateComments__VariantProps,
    internalArgProps: PlasmicTemplateComments__ArgProps,
  }
);

export default PlasmicTemplateComments;
/* prettier-ignore-end */
