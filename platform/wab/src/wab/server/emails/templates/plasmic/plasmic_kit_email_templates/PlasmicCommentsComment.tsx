/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: NUaRKxTU3gYx

import * as React from "react";

import {
  Flex as Flex__,
  PlasmicImg as PlasmicImg__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import {
  EmailColumn,
  EmailImage,
  EmailMarkdown,
  EmailRow,
  EmailText,
} from "@/wab/server/emails/components.tsx"; // plasmic-import: xWkqfjbn8oyX/codeComponent

createPlasmicElementProxy;

export type PlasmicCommentsComment__VariantMembers = {};
export type PlasmicCommentsComment__VariantsArgs = {};
type VariantPropType = keyof PlasmicCommentsComment__VariantsArgs;
export const PlasmicCommentsComment__VariantProps =
  new Array<VariantPropType>();

export type PlasmicCommentsComment__ArgsType = {
  avatarUrl?: React.ComponentProps<typeof PlasmicImg__>["src"];
  name?: string;
  content?: string;
  emoji?: string;
  aboutCommentText?: string;
};
type ArgPropType = keyof PlasmicCommentsComment__ArgsType;
export const PlasmicCommentsComment__ArgProps = new Array<ArgPropType>(
  "avatarUrl",
  "name",
  "content",
  "emoji",
  "aboutCommentText"
);

export type PlasmicCommentsComment__OverridesType = {
  root?: Flex__<typeof EmailRow>;
  emailRow?: Flex__<typeof EmailRow>;
  emailImage?: Flex__<typeof EmailImage>;
  emailMarkdown?: Flex__<typeof EmailMarkdown>;
};

export interface DefaultCommentsCommentProps {
  avatarUrl?: React.ComponentProps<typeof PlasmicImg__>["src"];
  name?: string;
  content?: string;
  emoji?: string;
  aboutCommentText?: string;
  className?: string;
}

const $$ = {};

function PlasmicCommentsComment__RenderFunc(props: {
  variants: PlasmicCommentsComment__VariantsArgs;
  args: PlasmicCommentsComment__ArgsType;
  overrides: PlasmicCommentsComment__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          avatarUrl: {
            src: "https://img.plasmic.app/img-optimizer/v1/img/f86d5d7ae700c37dd8db36806074f231.png",
            fullWidth: 600,
            fullHeight: 600,
          },
          content: ``,
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <EmailRow
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        "__wab_instance",
        "root_reset_taNK5uwsoPrzfpYmBVwUwX",
        "plasmic_default_styles",
        "plasmic_mixins",
        "plasmic_tokens",
        "CommentsComment__root__pLkHm"
      )}
    >
      {(() => {
        try {
          return $props.aboutCommentText;
        } catch (e) {
          if (
            e instanceof TypeError ||
            e?.plasmicType === "PlasmicUndefinedDataError"
          ) {
            return true;
          }
          throw e;
        }
      })() ? (
        <EmailText
          children={args.aboutCommentText}
          className={classNames(
            "__wab_instance",
            "CommentsComment__emailText__gvgXb"
          )}
          style={{
            fontSize: 11,
            marginBottom: 12,
            marginTop: 0,
            lineHeight: "16px",
          }}
        />
      ) : null}
      <EmailRow
        data-plasmic-name={"emailRow"}
        data-plasmic-override={overrides.emailRow}
        className={classNames(
          "__wab_instance",
          "CommentsComment__emailRow___8Ibd0"
        )}
      >
        <EmailColumn
          align={"left"}
          className={classNames(
            "__wab_instance",
            "CommentsComment__emailColumn___1Wfuy"
          )}
          style={{ width: 56 }}
          valign={"top"}
        >
          {(() => {
            try {
              return !$props.emoji;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return true;
              }
              throw e;
            }
          })() ? (
            <EmailImage
              data-plasmic-name={"emailImage"}
              data-plasmic-override={overrides.emailImage}
              className={classNames(
                "__wab_instance",
                "CommentsComment__emailImage___3CGp2"
              )}
              src={(() => {
                try {
                  return (
                    $props.avatarUrl.src ??
                    "https://img.plasmic.app/img-optimizer/v1/img/f86d5d7ae700c37dd8db36806074f231.png"
                  );
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              style={{ borderRadius: "100%" }}
              width={"48"}
            />
          ) : null}
          {(() => {
            try {
              return $props.emoji;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return true;
              }
              throw e;
            }
          })() ? (
            <EmailText
              children={args.emoji}
              className={classNames(
                "__wab_instance",
                "CommentsComment__emailText__tZidO"
              )}
              style={{ fontSize: 40, margin: "0", lineHeight: 1.2 }}
            />
          ) : null}
        </EmailColumn>
        <EmailColumn
          className={classNames(
            "__wab_instance",
            "CommentsComment__emailColumn__tbeRs"
          )}
          valign={"top"}
        >
          <EmailText
            children={args.name}
            className={classNames(
              "__wab_instance",
              "CommentsComment__emailText__gq4DD"
            )}
            style={{ fontWeight: 500, margin: 0, fontSize: 16 }}
          />

          <EmailMarkdown
            data-plasmic-name={"emailMarkdown"}
            data-plasmic-override={overrides.emailMarkdown}
            children={args.content}
            className={classNames(
              "__wab_instance",
              "CommentsComment__emailMarkdown___2GLvC"
            )}
            markdownContainerStyles={{ fontSize: 14, whiteSpace: "normal" }}
            markdownCustomStyles={{ p: { fontSize: 14, margin: 0 } }}
          />
        </EmailColumn>
      </EmailRow>
    </EmailRow>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "emailRow", "emailImage", "emailMarkdown"],
  emailRow: ["emailRow", "emailImage", "emailMarkdown"],
  emailImage: ["emailImage"],
  emailMarkdown: ["emailMarkdown"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof EmailRow;
  emailRow: typeof EmailRow;
  emailImage: typeof EmailImage;
  emailMarkdown: typeof EmailMarkdown;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicCommentsComment__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicCommentsComment__VariantsArgs;
    args?: PlasmicCommentsComment__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicCommentsComment__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicCommentsComment__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicCommentsComment__ArgProps,
          internalVariantPropNames: PlasmicCommentsComment__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicCommentsComment__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicCommentsComment";
  } else {
    func.displayName = `PlasmicCommentsComment.${nodeName}`;
  }
  return func;
}

export const PlasmicCommentsComment = Object.assign(
  // Top-level PlasmicCommentsComment renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    emailRow: makeNodeComponent("emailRow"),
    emailImage: makeNodeComponent("emailImage"),
    emailMarkdown: makeNodeComponent("emailMarkdown"),

    // Metadata about props expected for PlasmicCommentsComment
    internalVariantProps: PlasmicCommentsComment__VariantProps,
    internalArgProps: PlasmicCommentsComment__ArgProps,
  }
);

export default PlasmicCommentsComment;
/* prettier-ignore-end */
