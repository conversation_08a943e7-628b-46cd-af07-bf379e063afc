/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: iVIFERpAtmin

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import { EmailRow } from "@/wab/server/emails/components.tsx"; // plasmic-import: xWkqfjbn8oyX/codeComponent
import FooterSocialIcon from "../../FooterSocialIcon"; // plasmic-import: 8Wh2IAEsoffQ/component

createPlasmicElementProxy;

export type PlasmicFooterSocialIcons__VariantMembers = {};
export type PlasmicFooterSocialIcons__VariantsArgs = {};
type VariantPropType = keyof PlasmicFooterSocialIcons__VariantsArgs;
export const PlasmicFooterSocialIcons__VariantProps =
  new Array<VariantPropType>();

export type PlasmicFooterSocialIcons__ArgsType = {};
type ArgPropType = keyof PlasmicFooterSocialIcons__ArgsType;
export const PlasmicFooterSocialIcons__ArgProps = new Array<ArgPropType>();

export type PlasmicFooterSocialIcons__OverridesType = {
  root?: Flex__<typeof EmailRow>;
};

export interface DefaultFooterSocialIconsProps {
  className?: string;
}

const $$ = {};

function PlasmicFooterSocialIcons__RenderFunc(props: {
  variants: PlasmicFooterSocialIcons__VariantsArgs;
  args: PlasmicFooterSocialIcons__ArgsType;
  overrides: PlasmicFooterSocialIcons__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <EmailRow
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        "__wab_instance",
        "root_reset_taNK5uwsoPrzfpYmBVwUwX",
        "plasmic_default_styles",
        "plasmic_mixins",
        "plasmic_tokens",
        "FooterSocialIcons__root___9Z9Et"
      )}
      style={{ width: 192, padding: 8 }}
    >
      <FooterSocialIcon
        className={classNames(
          "__wab_instance",
          "FooterSocialIcons__footerSocialIcon__sbtBt"
        )}
        linkTo={"https://x.com/plasmicapp"}
        logo={{
          src: "https://img.plasmic.app/img-optimizer/v1/img/36f6984cd7458dbf21cf4ba3f71e5ce9.png",
          fullWidth: 32,
          fullHeight: 32,
        }}
        name={"Twitter"}
      />

      <FooterSocialIcon
        className={classNames(
          "__wab_instance",
          "FooterSocialIcons__footerSocialIcon__k86"
        )}
        linkTo={"https://www.youtube.com/c/plasmicapp"}
        logo={{
          src: "https://img.plasmic.app/img-optimizer/v1/img/1a06655bab2223c4b1b2a14367c0edb7.png",
          fullWidth: 32,
          fullHeight: 32,
        }}
        name={"Youtube"}
      />

      <FooterSocialIcon
        className={classNames(
          "__wab_instance",
          "FooterSocialIcons__footerSocialIcon__lk4Ss"
        )}
        linkTo={"https://www.plasmic.app/slack"}
        logo={{
          src: "https://img.plasmic.app/img-optimizer/v1/img/6352b7f9fb010445f532024b0ad061fc.png",
          fullWidth: 32,
          fullHeight: 32,
        }}
        name={"Slack"}
      />

      <FooterSocialIcon
        className={classNames(
          "__wab_instance",
          "FooterSocialIcons__footerSocialIcon__g7JVb"
        )}
        linkTo={"https://www.linkedin.com/company/plasmicapp"}
        logo={{
          src: "https://img.plasmic.app/img-optimizer/v1/img/3016b986244ded7eaf741c9ea317bc20.png",
          fullWidth: 32,
          fullHeight: 32,
        }}
        name={"Linkedin"}
      />
    </EmailRow>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof EmailRow;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicFooterSocialIcons__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicFooterSocialIcons__VariantsArgs;
    args?: PlasmicFooterSocialIcons__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicFooterSocialIcons__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicFooterSocialIcons__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicFooterSocialIcons__ArgProps,
          internalVariantPropNames: PlasmicFooterSocialIcons__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicFooterSocialIcons__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicFooterSocialIcons";
  } else {
    func.displayName = `PlasmicFooterSocialIcons.${nodeName}`;
  }
  return func;
}

export const PlasmicFooterSocialIcons = Object.assign(
  // Top-level PlasmicFooterSocialIcons renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicFooterSocialIcons
    internalVariantProps: PlasmicFooterSocialIcons__VariantProps,
    internalArgProps: PlasmicFooterSocialIcons__ArgProps,
  }
);

export default PlasmicFooterSocialIcons;
/* prettier-ignore-end */
