/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: 8Wh2IAEsoffQ

import * as React from "react";

import {
  Flex as Flex__,
  PlasmicImg as PlasmicImg__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import {
  EmailColumn,
  EmailImage,
  EmailLink,
} from "@/wab/server/emails/components.tsx"; // plasmic-import: pVqOLFd6uDdb/codeComponent

createPlasmicElementProxy;

export type PlasmicFooterSocialIcon__VariantMembers = {};
export type PlasmicFooterSocialIcon__VariantsArgs = {};
type VariantPropType = keyof PlasmicFooterSocialIcon__VariantsArgs;
export const PlasmicFooterSocialIcon__VariantProps =
  new Array<VariantPropType>();

export type PlasmicFooterSocialIcon__ArgsType = {
  logo?: React.ComponentProps<typeof PlasmicImg__>["src"];
  name?: string;
  linkTo?: string;
};
type ArgPropType = keyof PlasmicFooterSocialIcon__ArgsType;
export const PlasmicFooterSocialIcon__ArgProps = new Array<ArgPropType>(
  "logo",
  "name",
  "linkTo"
);

export type PlasmicFooterSocialIcon__OverridesType = {
  root?: Flex__<typeof EmailColumn>;
  emailLink?: Flex__<typeof EmailLink>;
  emailImage?: Flex__<typeof EmailImage>;
};

export interface DefaultFooterSocialIconProps {
  logo?: React.ComponentProps<typeof PlasmicImg__>["src"];
  name?: string;
  linkTo?: string;
  className?: string;
}

const $$ = {};

function PlasmicFooterSocialIcon__RenderFunc(props: {
  variants: PlasmicFooterSocialIcon__VariantsArgs;
  args: PlasmicFooterSocialIcon__ArgsType;
  overrides: PlasmicFooterSocialIcon__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <EmailColumn
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      align={"center"}
      className={classNames(
        "__wab_instance",
        "root_reset_taNK5uwsoPrzfpYmBVwUwX",
        "plasmic_default_styles",
        "plasmic_mixins",
        "plasmic_tokens",
        "FooterSocialIcon__root__moWDd"
      )}
      style={{ padding: 8 }}
    >
      <EmailLink
        data-plasmic-name={"emailLink"}
        data-plasmic-override={overrides.emailLink}
        className={classNames(
          "__wab_instance",
          "FooterSocialIcon__emailLink___9Lzy0"
        )}
        href={args.linkTo}
        image={
          <EmailImage
            data-plasmic-name={"emailImage"}
            data-plasmic-override={overrides.emailImage}
            alt={(() => {
              try {
                return `${$props.name} logo`;
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return ``;
                }
                throw e;
              }
            })()}
            className={classNames(
              "__wab_instance",
              "FooterSocialIcon__emailImage___0IsWh"
            )}
            src={(() => {
              try {
                return $props.logo?.src;
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return undefined;
                }
                throw e;
              }
            })()}
            width={"16"}
          />
        }
        type={"image"}
      />
    </EmailColumn>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "emailLink", "emailImage"],
  emailLink: ["emailLink", "emailImage"],
  emailImage: ["emailImage"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof EmailColumn;
  emailLink: typeof EmailLink;
  emailImage: typeof EmailImage;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicFooterSocialIcon__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicFooterSocialIcon__VariantsArgs;
    args?: PlasmicFooterSocialIcon__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicFooterSocialIcon__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicFooterSocialIcon__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicFooterSocialIcon__ArgProps,
          internalVariantPropNames: PlasmicFooterSocialIcon__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicFooterSocialIcon__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicFooterSocialIcon";
  } else {
    func.displayName = `PlasmicFooterSocialIcon.${nodeName}`;
  }
  return func;
}

export const PlasmicFooterSocialIcon = Object.assign(
  // Top-level PlasmicFooterSocialIcon renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    emailLink: makeNodeComponent("emailLink"),
    emailImage: makeNodeComponent("emailImage"),

    // Metadata about props expected for PlasmicFooterSocialIcon
    internalVariantProps: PlasmicFooterSocialIcon__VariantProps,
    internalArgProps: PlasmicFooterSocialIcon__ArgProps,
  }
);

export default PlasmicFooterSocialIcon;
/* prettier-ignore-end */
