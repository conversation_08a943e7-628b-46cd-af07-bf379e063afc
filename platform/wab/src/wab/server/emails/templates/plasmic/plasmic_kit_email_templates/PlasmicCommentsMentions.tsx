/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: xmHJRn1lA9BZ

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import { EmailRow, EmailSection } from "@/wab/server/emails/components.tsx"; // plasmic-import: ccI_uNhThjcT/codeComponent
import AtomsButtonOutline from "../../AtomsButtonOutline"; // plasmic-import: 5OdShbT02UoK/component
import AtomsCard from "../../AtomsCard"; // plasmic-import: 2cidd9ockVoM/component
import CommentsComment from "../../CommentsComment"; // plasmic-import: NUaRKxTU3gYx/component
import CommentsHeader from "../../CommentsHeader"; // plasmic-import: aekteDGKbn5u/component

createPlasmicElementProxy;

export type PlasmicCommentsMentions__VariantMembers = {};
export type PlasmicCommentsMentions__VariantsArgs = {};
type VariantPropType = keyof PlasmicCommentsMentions__VariantsArgs;
export const PlasmicCommentsMentions__VariantProps =
  new Array<VariantPropType>();

export type PlasmicCommentsMentions__ArgsType = {
  projectUrl?: string;
  comments?: any;
};
type ArgPropType = keyof PlasmicCommentsMentions__ArgsType;
export const PlasmicCommentsMentions__ArgProps = new Array<ArgPropType>(
  "projectUrl",
  "comments"
);

export type PlasmicCommentsMentions__OverridesType = {
  root?: Flex__<typeof EmailSection>;
  commentsHeader?: Flex__<typeof CommentsHeader>;
  emailRow?: Flex__<typeof EmailRow>;
  atomsCard?: Flex__<typeof AtomsCard>;
  commentsComment?: Flex__<typeof CommentsComment>;
  atomsButtonOutline?: Flex__<typeof AtomsButtonOutline>;
};

export interface DefaultCommentsMentionsProps {
  projectUrl?: string;
  comments?: any;
  className?: string;
}

const $$ = {};

function PlasmicCommentsMentions__RenderFunc(props: {
  variants: PlasmicCommentsMentions__VariantsArgs;
  args: PlasmicCommentsMentions__ArgsType;
  overrides: PlasmicCommentsMentions__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          projectUrl: "https://plasmic.app",
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <EmailSection
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        "__wab_instance",
        "root_reset_taNK5uwsoPrzfpYmBVwUwX",
        "plasmic_default_styles",
        "plasmic_mixins",
        "plasmic_tokens",
        "CommentsMentions__root__aduz6"
      )}
      style={{ marginBottom: 48 }}
    >
      <CommentsHeader
        data-plasmic-name={"commentsHeader"}
        data-plasmic-override={overrides.commentsHeader}
        className={classNames(
          "__wab_instance",
          "CommentsMentions__commentsHeader__dvdkC"
        )}
        content={(() => {
          try {
            return (() => {
              const singular =
                [...new Set($props.comments.map((c) => c.name))].length === 1;
              return `${$props.comments[0]?.name}${
                singular ? "" : " and others"
              } mentioned you in ${
                $props.comments.length === 1
                  ? "a comment"
                  : $props.comments.length + " comments"
              }`;
            })();
          } catch (e) {
            if (
              e instanceof TypeError ||
              e?.plasmicType === "PlasmicUndefinedDataError"
            ) {
              return undefined;
            }
            throw e;
          }
        })()}
      />

      {((_par) => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
        (() => {
          try {
            return $props.comments;
          } catch (e) {
            if (
              e instanceof TypeError ||
              e?.plasmicType === "PlasmicUndefinedDataError"
            ) {
              return [];
            }
            throw e;
          }
        })()
      ).map((__plasmic_item_0, __plasmic_idx_0) => {
        const currentItem = __plasmic_item_0;
        const currentIndex = __plasmic_idx_0;
        return (
          <EmailRow
            data-plasmic-name={"emailRow"}
            data-plasmic-override={overrides.emailRow}
            className={classNames(
              "__wab_instance",
              "CommentsMentions__emailRow__kTaZ"
            )}
            key={currentIndex}
            style={{ marginBottom: 16 }}
          >
            <AtomsCard
              data-plasmic-name={"atomsCard"}
              data-plasmic-override={overrides.atomsCard}
              className={classNames(
                "__wab_instance",
                "CommentsMentions__atomsCard___31CrV"
              )}
            >
              <CommentsComment
                data-plasmic-name={"commentsComment"}
                data-plasmic-override={overrides.commentsComment}
                avatarUrl={(() => {
                  try {
                    return currentItem.avatarUrl;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
                className={classNames(
                  "__wab_instance",
                  "CommentsMentions__commentsComment___4DnCb"
                )}
                content={(() => {
                  try {
                    return currentItem.comment;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
                name={(() => {
                  try {
                    return currentItem.name;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()}
              />
            </AtomsCard>
          </EmailRow>
        );
      })}
      <AtomsButtonOutline
        data-plasmic-name={"atomsButtonOutline"}
        data-plasmic-override={overrides.atomsButtonOutline}
        className={classNames(
          "__wab_instance",
          "CommentsMentions__atomsButtonOutline__j2AiO"
        )}
        href={args.projectUrl}
        text={"View in Plasmic"}
      />
    </EmailSection>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "commentsHeader",
    "emailRow",
    "atomsCard",
    "commentsComment",
    "atomsButtonOutline",
  ],
  commentsHeader: ["commentsHeader"],
  emailRow: ["emailRow", "atomsCard", "commentsComment"],
  atomsCard: ["atomsCard", "commentsComment"],
  commentsComment: ["commentsComment"],
  atomsButtonOutline: ["atomsButtonOutline"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof EmailSection;
  commentsHeader: typeof CommentsHeader;
  emailRow: typeof EmailRow;
  atomsCard: typeof AtomsCard;
  commentsComment: typeof CommentsComment;
  atomsButtonOutline: typeof AtomsButtonOutline;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicCommentsMentions__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicCommentsMentions__VariantsArgs;
    args?: PlasmicCommentsMentions__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicCommentsMentions__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicCommentsMentions__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicCommentsMentions__ArgProps,
          internalVariantPropNames: PlasmicCommentsMentions__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicCommentsMentions__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicCommentsMentions";
  } else {
    func.displayName = `PlasmicCommentsMentions.${nodeName}`;
  }
  return func;
}

export const PlasmicCommentsMentions = Object.assign(
  // Top-level PlasmicCommentsMentions renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    commentsHeader: makeNodeComponent("commentsHeader"),
    emailRow: makeNodeComponent("emailRow"),
    atomsCard: makeNodeComponent("atomsCard"),
    commentsComment: makeNodeComponent("commentsComment"),
    atomsButtonOutline: makeNodeComponent("atomsButtonOutline"),

    // Metadata about props expected for PlasmicCommentsMentions
    internalVariantProps: PlasmicCommentsMentions__VariantProps,
    internalArgProps: PlasmicCommentsMentions__ArgProps,
  }
);

export default PlasmicCommentsMentions;
/* prettier-ignore-end */
