/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: taNK5uwsoPrzfpYmBVwUwX
// Component: akS-yd3gJU3j

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import { EmailRow, EmailSection } from "@/wab/server/emails/components.tsx"; // plasmic-import: ccI_uNhThjcT/codeComponent
import AtomsCard from "../../AtomsCard"; // plasmic-import: 2cidd9ockVoM/component
import CommentsComment from "../../CommentsComment"; // plasmic-import: NUaRKxTU3gYx/component

createPlasmicElementProxy;

export type PlasmicCommentsReactions__VariantMembers = {};
export type PlasmicCommentsReactions__VariantsArgs = {};
type VariantPropType = keyof PlasmicCommentsReactions__VariantsArgs;
export const PlasmicCommentsReactions__VariantProps =
  new Array<VariantPropType>();

export type PlasmicCommentsReactions__ArgsType = {
  projectUrl?: string;
  reactions?: any;
  comment?: string;
  name?: string;
};
type ArgPropType = keyof PlasmicCommentsReactions__ArgsType;
export const PlasmicCommentsReactions__ArgProps = new Array<ArgPropType>(
  "projectUrl",
  "reactions",
  "comment",
  "name"
);

export type PlasmicCommentsReactions__OverridesType = {
  root?: Flex__<typeof EmailSection>;
  emailRow?: Flex__<typeof EmailRow>;
  atomsCard?: Flex__<typeof AtomsCard>;
  commentsComment?: Flex__<typeof CommentsComment>;
};

export interface DefaultCommentsReactionsProps {
  projectUrl?: string;
  reactions?: any;
  comment?: string;
  name?: string;
  className?: string;
}

const $$ = {};

function PlasmicCommentsReactions__RenderFunc(props: {
  variants: PlasmicCommentsReactions__VariantsArgs;
  args: PlasmicCommentsReactions__ArgsType;
  overrides: PlasmicCommentsReactions__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          projectUrl: "https://plasmic.app",
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <EmailSection
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        "__wab_instance",
        "root_reset_taNK5uwsoPrzfpYmBVwUwX",
        "plasmic_default_styles",
        "plasmic_mixins",
        "plasmic_tokens",
        "CommentsReactions__root___1OJn"
      )}
      style={{ marginBottom: 16 }}
    >
      <EmailRow
        data-plasmic-name={"emailRow"}
        data-plasmic-override={overrides.emailRow}
        className={classNames(
          "__wab_instance",
          "CommentsReactions__emailRow__u657R"
        )}
      >
        <AtomsCard
          data-plasmic-name={"atomsCard"}
          data-plasmic-override={overrides.atomsCard}
          className={classNames(
            "__wab_instance",
            "CommentsReactions__atomsCard__muswf"
          )}
        >
          <CommentsComment
            data-plasmic-name={"commentsComment"}
            data-plasmic-override={overrides.commentsComment}
            aboutCommentText={(() => {
              try {
                return (() => {
                  const participants = [
                    ...new Set($props.reactions.map((r) => r.name)),
                  ];
                  const singular = participants.length === 1;
                  return `${$props.reactions[0].name}${
                    singular
                      ? ""
                      : " and " + (participants.length - 1) + " others"
                  } reacted to your comment`;
                })();
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return undefined;
                }
                throw e;
              }
            })()}
            className={classNames(
              "__wab_instance",
              "CommentsReactions__commentsComment___7NyJ1"
            )}
            content={args.comment}
            emoji={(() => {
              try {
                return $props.reactions[0].emoji;
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return undefined;
                }
                throw e;
              }
            })()}
            name={args.name}
          />
        </AtomsCard>
      </EmailRow>
    </EmailSection>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "emailRow", "atomsCard", "commentsComment"],
  emailRow: ["emailRow", "atomsCard", "commentsComment"],
  atomsCard: ["atomsCard", "commentsComment"],
  commentsComment: ["commentsComment"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: typeof EmailSection;
  emailRow: typeof EmailRow;
  atomsCard: typeof AtomsCard;
  commentsComment: typeof CommentsComment;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicCommentsReactions__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicCommentsReactions__VariantsArgs;
    args?: PlasmicCommentsReactions__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicCommentsReactions__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicCommentsReactions__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicCommentsReactions__ArgProps,
          internalVariantPropNames: PlasmicCommentsReactions__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicCommentsReactions__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicCommentsReactions";
  } else {
    func.displayName = `PlasmicCommentsReactions.${nodeName}`;
  }
  return func;
}

export const PlasmicCommentsReactions = Object.assign(
  // Top-level PlasmicCommentsReactions renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    emailRow: makeNodeComponent("emailRow"),
    atomsCard: makeNodeComponent("atomsCard"),
    commentsComment: makeNodeComponent("commentsComment"),

    // Metadata about props expected for PlasmicCommentsReactions
    internalVariantProps: PlasmicCommentsReactions__VariantProps,
    internalArgProps: PlasmicCommentsReactions__ArgProps,
  }
);

export default PlasmicCommentsReactions;
/* prettier-ignore-end */
