import { isString, mapValues, pick } from "lodash";
import { IValidation } from "typia";
import { createMapFromObject, filterObject } from "@/wab/shared/collections";
import {
  check,
  ensure,
  ensureArray,
  isJsonScalar,
  maybeTry,
  switchType,
  todo,
  unexpected,
} from "@/wab/shared/common";
import { arrayReversed } from "@/wab/commons/collections";
import { getParamByVarName } from "@/wab/shared/core/components";
import { getProjectFlags } from "@/wab/shared/devflags";
import {
  codeLit,
  customCode,
  summarizeExpr,
  summarizePath,
} from "@/wab/shared/core/exprs";
import { cloneSite } from "@/wab/shared/core/sites";
import { UpdateVariableOperations, mkInteraction } from "@/wab/shared/core/states";
import {
  isTplTag,
  mkTplComponentX,
  mkTplInlinedText,
  mkTplTagX,
  reconnectChildren,
} from "@/wab/shared/core/tpls";
import { DATA_SOURCE_LOWER } from "@/wab/shared/Labels";
import { isSlot } from "@/wab/shared/SlotUtils";
import { TplMgr, addEmptyQuery } from "@/wab/shared/TplMgr";
import { $$$ } from "@/wab/shared/TplQuery";
import {
  mkDataSourceOpExpr,
  mkDataSourceTemplate,
} from "@/wab/shared/data-sources-meta/data-sources";
import {
  getDynamicStringSegments,
  isDynamicValue,
} from "@/wab/shared/dynamic-bindings";
import { pathToString } from "@/wab/shared/eval/expression-parser";
import {
  Component,
  CustomCode,
  DataSourceOpExpr,
  EventHandler,
  Expr,
  ObjectPath,
  RenderExpr,
  TemplatedString,
  TplNode,
  isKnownComponent,
  isKnownRenderExpr,
} from "@/wab/shared/model/classes";
import { genSiteErrors } from "@/wab/shared/site-invariants";
import { Issue } from "@/wab/shared/copilot/prompt-utils";
import { ChangeCtx, NOOP_CHANGE_CTX } from "@/wab/shared/copilot/internal/ChangeCtx";
import {
  Desummarizer,
  Summarizer,
  SummaryComputation,
} from "@/wab/shared/copilot/internal/prompt-summarizer";
import {
  CommandsStruct,
  ToyAction,
  ToyDataSourceOp,
  ToyElement,
  ToyExpr,
} from "@/wab/shared/copilot/internal/toy-types";
import {
  validateAction,
  validateCommandsStruct,
  validateElement,
} from "@/wab/shared/copilot/internal/validators/gen/toy-types-validator";
import { isSlotElt, simplifyCommands } from "@/wab/shared/copilot/internal/sandbox/prompts";

type OpType = "select" | "select-one" | "insert" | "update" | "delete" | "run";

const opTypeToOpName: Map<OpType, string> = createMapFromObject({
  select: "getList",
  "select-one": "getOne",
  insert: "create",
  update: "updateById",
  delete: "deleteById",
  // TODO this can be a customWrite as well...
  run: "customRead",
});

// const ToyExprV = z.any();
//
// const ToyElementV = z.object({
//   component: z.string(),
//   name: z.string().optional(),
//   props: z.record(ToyExprV),
// });
//
// function isToyElement(x: any): x is ToyElement {
//   return !!ToyElementV.parse(x);
// }

export interface RunCommandsResult {
  issues: Issue[];
}

export async function runCommands(
  commandData: CommandsStruct,
  { rootSummary, bookkeeping }: SummaryComputation,
  summarizer: Summarizer,
  currentComponentName: string,
  opts?: {
    changeCtx?: ChangeCtx;
    speculative?: boolean;
    continueOnError?: boolean;
  }
) {
  const {
    speculative = false,
    continueOnError = false,
    changeCtx = NOOP_CHANGE_CTX,
  } = opts ?? {};
  const site = speculative ? cloneSite(summarizer.site) : summarizer.site;
  summarizer = speculative
    ? new Summarizer(
        site,
        summarizer.dataSources,
        summarizer.tablesByDataSourceName
      )
    : summarizer;
  ({ rootSummary, bookkeeping } = speculative
    ? summarizer.summarizeWithContext(currentComponentName)
    : { rootSummary, bookkeeping });

  const tplMgr = new TplMgr({ site });
  commandData = simplifyCommands(commandData);

  const currentComponent = ensure(
    site.components.find((c) => c.name === currentComponentName),
    ""
  );

  function checkExists<T extends string | ObjectPath | CustomCode>(
    exprRaw: T,
    mustBePath = false
  ): T {
    const expr: string | ObjectPath | CustomCode = exprRaw;
    const extractedPath = switchType(expr)
      .when(String, (p) => p.split("."))
      .when(ObjectPath, (p) => p.path)
      .when(CustomCode, (p) => {
        const body = p.code.slice(1, -1);
        if (body.match(/^\s*\w+(\s*\.\s*\w+)*\s*$/g)) {
          return body.split(/\s*\.\s*/g);
        } else {
          return undefined;
        }
      })
      .result();
    if (!extractedPath) {
      if (mustBePath) {
        result.issues.push({
          message: `Expected a path expression but got ${
            isString(expr)
              ? expr
              : summarizeExpr(expr, {
                  projectFlags: getProjectFlags(site),
                  component: null,
                  inStudio: true,
                })
          }`,
          severity: "error",
        });
      }
    } else {
      const env = new Set([
        ...currentComponent.params.map((p) => `$props.${p.variable.name}`),
        ...currentComponent.dataQueries.map((q) => `$queries.${q.name}`),
        ...currentComponent.states.map((s) =>
          s.tplNode
            ? `$state.${s.tplNode.name}.${s.param.variable.name}`
            : `$state.${s.param.variable.name}`
        ),
      ]);
      if (!env.has(extractedPath.join("."))) {
        result.issues.push({
          message: `Got path expression ${extractedPath.join(
            "."
          )} which does not exist`,
          severity: "error",
        });
      }
    }
    return exprRaw;
  }

  const detoyAction = (action: ToyAction, eventHandler: EventHandler) => {
    logValidate(action, "Action", validateAction);
    switch (action.action) {
      case "NavTo": {
        return mkInteraction(eventHandler, "navigation", "Go to page", {
          destination: convertStringExpr(action.path),
        });
      }
      case "SetState": {
        const variable = checkExists(
          new ObjectPath({
            path: action.name.split("."),
            fallback: undefined,
          })
        );
        return mkInteraction(
          eventHandler,
          "updateVariable",
          `Update ${summarizePath(variable)}`,
          {
            variable,
            operation: codeLit(UpdateVariableOperations.NewValue),
            value: convertExpr(action.expr, null, null),
          }
        );
      }
      case "RunDataSourceOp": {
        return mkInteraction(
          eventHandler,
          "dataSourceOp",
          `Use ${DATA_SOURCE_LOWER}`,
          {
            continueOnError: codeLit(false),
            dataOp: convertDataSourceOp(action.op),
          }
        );
      }
    }
  };

  function isRealProp(tagOrComponent: Component | string, prop: string) {
    return (
      !isKnownComponent(tagOrComponent) ||
      !!getParamByVarName(tagOrComponent, prop)
    );
  }

  function tryGetTplByAutoName(autoName: string, strict = false) {
    const tpl = desummarizer.getTplByAutoName(autoName);
    if (!tpl) {
      result.issues.push({
        message: `Element named ${autoName} does not exist`,
        severity: strict ? "error" : "warning",
      });
    }
    return tpl;
  }
  function getTplByAutoName(autoName: string) {
    return ensure(tryGetTplByAutoName(autoName, true), "");
  }

  function isSlotProp(tagOrComponent: Component | string, prop: string) {
    return (
      (isKnownComponent(tagOrComponent) &&
        isSlot(ensure(getParamByVarName(tagOrComponent, prop), ""))) ||
      prop === "children"
    );
  }

  function convertStringExpr(expr: string, tryAsCode?: false): TemplatedString;
  function convertStringExpr(
    expr: string,
    tryAsCode: true
  ): CustomCode | TemplatedString;
  function convertStringExpr(expr: string, tryAsCode = false) {
    const segments = getDynamicStringSegments(expr);
    if (tryAsCode && segments.length === 1 && isDynamicValue(expr[0])) {
      return customCode(expr);
    } else {
      return new TemplatedString({
        text: segments.map((seg) =>
          isDynamicValue(seg)
            ? customCode(checkExists(seg.replace(/^{{/, "").replace(/}}$/, "")))
            : seg
        ),
      });
    }
  }

  const currentLoc: (string | number)[] = ["commands"];
  function getCurrentLoc() {
    return pathToString(currentLoc);
  }
  function withLoc<T>(loc: string | number, f: () => T) {
    currentLoc.push(loc);
    try {
      return f();
    } finally {
      currentLoc.pop();
    }
  }

  function logValidate<T>(
    x: T,
    typeName: string,
    validator: (x: T) => IValidation<T>
  ) {
    const valRes = validator(x);

    if (!valRes.success) {
      result.issues.push({
        message: `Type-check failed for ${typeName} in ${getCurrentLoc()}`,
        details: valRes.errors,
      });
    }
    return x;
  }

  function convertExpr(
    expr: ToyExpr,
    tagOrComponent: Component | string | null,
    prop: string | null
  ): Expr {
    return tagOrComponent && prop && isSlotProp(tagOrComponent, prop)
      ? new RenderExpr({
          tpl: ensureArray(expr).map((elt) => elementToTpl(elt)),
        })
      : isKnownComponent(tagOrComponent) &&
        prop &&
        (ensure(getParamByVarName(tagOrComponent, prop), "").type.name ===
          "func" ||
          prop.startsWith("on"))
      ? (() => {
          const eventHandler = new EventHandler({
            interactions: [],
          });
          eventHandler.interactions.push(
            ...ensureArray(expr).map((action) =>
              detoyAction(action, eventHandler)
            )
          );
          return eventHandler;
        })()
      : isString(expr)
      ? convertStringExpr(expr)
      : isJsonScalar(expr)
      ? codeLit(expr)
      : Array.isArray(expr)
      ? codeLit(expr)
      : unexpected();
  }

  function commandPropsToRealProps(
    props: Record<string, ToyExpr>,
    componentName: string,
    elementName: string
  ): Record<string, Expr> {
    const tagOrComponent: Component | string = componentName.match(/^[a-z]/)
      ? componentName
      : desummarizer.getComponentByAutoName(componentName);
    for (const prop of Object.keys(props)) {
      if (!isRealProp(tagOrComponent, prop)) {
        result.issues.push({
          message: `Unexpected prop ${prop} for component ${componentName} on element ${elementName}`,
        });
      }
    }
    return mapValues(
      filterObject(props, ([prop, expr]) => isRealProp(tagOrComponent, prop)),
      (expr, prop) => convertExpr(expr, tagOrComponent, prop)
    );
  }

  function elementToTpl(elt: ToyElement) {
    logValidate(elt, "Element", validateElement);
    const normName = (name: string) => {
      if (name.startsWith("*")) {
        return name.substring(1);
      }
      return name;
    };
    if (isSlotElt(elt)) {
      return todo("mkTplSlot");
    }
    if (typeof elt === "string") {
      return mkTplInlinedText(elt, [
        tplMgr.ensureBaseVariant(currentComponent),
      ]);
    }
    if (elt.component.match(/^[a-z]/)) {
      const children: TplNode[] = [];
      const attrs = commandPropsToRealProps(elt.props, elt.component, elt.name);
      [...Object.entries(attrs)].forEach(([prop, expr]) => {
        if (isKnownRenderExpr(expr)) {
          children.push(...expr.tpl);
          delete attrs[prop];
        }
      });
      const tpl = mkTplTagX(elt.component, {
        name: normName(elt.name),
        baseVariant: tplMgr.ensureBaseVariant(currentComponent),
        attrs,
      });
      tpl.children = children;
      reconnectChildren(tpl);
      return tpl;
    } else {
      return mkTplComponentX({
        name: normName(elt.name),
        component: desummarizer.getComponentByAutoName(elt.component),
        baseVariant: tplMgr.ensureBaseVariant(currentComponent),
        args: commandPropsToRealProps(elt.props, elt.component, elt.name),
      });
    }
  }

  const desummarizer = new Desummarizer(
    rootSummary,
    bookkeeping,
    currentComponent
  );

  const result: RunCommandsResult = {
    issues: [],
  };

  logValidate(commandData, "CommandsStruct", validateCommandsStruct);

  function convertDataSourceOp(dsop: ToyDataSourceOp): DataSourceOpExpr {
    const dataSource = summarizer.getDataSourceByName(dsop.dataSource);

    switch (dsop.opType) {
      case "sql":
        // TODO parse SQL for other data sources
        check(
          dataSource.source === "postgres",
          "only postgres supported for now"
        );
        // TODO support non-SELECT queries
        return mkDataSourceOpExpr({
          opId: "", // TODO
          opName: "customRead",
          sourceId: dataSource.id,
          templates: {
            query: mkDataSourceTemplate({
              fieldType: "string",
              value: convertStringExpr(dsop.code),
              bindings: undefined,
            }),
          },
        });
      case "http":
        check(dataSource.source === "http");
        return todo();
      // mkDataSourceOpExpr(
      //     dataSource,
      //     {
      //       query: mkDataSourceTemplate({
      //         fieldType: "string",
      //         value: new TemplatedString({
      //           text: getDynamicStringSegments(dsop.body).map(
      //               (part) => (isDynamicValue(part) ? code(part) : part)
      //           ),
      //         }),
      //         bindings: undefined,
      //       })
      //     }
      // );
    }
  }

  await changeCtx.change(({ success, failure }) => {
    let commandIndex = -1;
    for (const command of commandData.commands) {
      commandIndex++;
      maybeTry(
        continueOnError,
        () => {
          withLoc(commandIndex, () => {
            switch (command.cmd) {
              case "InsertElements": {
                const newTpls: TplNode[] = [];
                for (const newElt of command.elements) {
                  const newTpl = elementToTpl(newElt);
                  newTpls.push(newTpl);
                  if (typeof newElt !== "string" && "name" in newElt) {
                    // Keep track of the name that the model generated for this
                    // element as its auto name, as subsequent commands may
                    // also reference it by the same name. Note that this name
                    // will differ from newTpl.name, since newTpl.name will be updated
                    // to be unique in the component.
                    desummarizer.trackTpl(newTpl, newElt.name);
                  }
                }
                const q = $$$(getTplByAutoName(command.of));
                if (command.location === "inside") {
                  newTpls.forEach((tpl) => q.append(tpl));
                } else if (command.location === "after") {
                  arrayReversed(newTpls).forEach((tpl) => q.after(tpl));
                } else if (command.location === "before") {
                  newTpls.forEach((tpl) => q.before(tpl));
                }
                for (const tpl of newTpls) {
                  tplMgr.ensureSubtreeCorrectlyNamed(currentComponent, tpl);
                }
                break;
              }
              case "SetProp": {
                const tpl = getTplByAutoName(command.element);
                if (isTplTag(tpl)) {
                  tpl.vsettings[0].attrs[command.prop] = convertExpr(
                    command.expr,
                    "div",
                    command.prop
                  );
                } else {
                  tplMgr.setArg(
                    tpl,
                    tpl.vsettings[0],
                    ensure(getParamByVarName(tpl.component, command.prop), "")
                      .variable,
                    convertExpr(command.expr, tpl.component, command.prop)
                  );
                }
                break;
              }
              case "AddQuery": {
                const query = addEmptyQuery(currentComponent);
                query.name = command.name;
                query.op = convertDataSourceOp(command.query);
                break;
              }
              case "AddState": {
                todo();
                break;
              }
              case "AddPage": {
                todo();
                break;
              }
              case "AddAction": {
                todo();
                break;
              }
              default:
                unexpected();
                break;
            }
          });
        },
        (error) => {
          result.issues.push({
            message: `Error running command ${command.cmd}: ${error.message}`,
            error: error.stack.split("\n"),
            severity: "error",
          });
        }
      );
    }

    const invariantErrors = Array.from(genSiteErrors(site));
    if (invariantErrors.length > 0) {
      result.issues.push({
        message: `Site invariant errors: ${invariantErrors}`,
        details: invariantErrors.map((er) => pick(er, "name", "message")),
        severity: "error",
      });
    }
    return success();
  });

  return result;
}
