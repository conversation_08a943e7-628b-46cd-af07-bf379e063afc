$scrollbar-thumb-color: rgba(0, 0, 0, 0.3);
$scrollbar-track-color: transparent;
html {
  overflow: hidden;
}

body {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

* {
  scrollbar-color: $scrollbar-thumb-color $scrollbar-track-color;
  scrollbar-width: thin;
}

.__non_interactive_canvas * {
  cursor: default !important;
  user-select: none;
  // Prevent resize-able textareas.
  resize: none;
}

*::-webkit-scrollbar {
  width: 10px;
}

*::-webkit-scrollbar-track {
  background: $scrollbar-track-color;
  pointer-events: none;
}

*::-webkit-scrollbar-thumb {
  background: $scrollbar-thumb-color;
  border: 1px solid white;
  border-radius: 9999px;
}

.__wab_root {
  display: flex;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
}

.__wab_root--stretch {
  padding: 0;
  align-items: flex-start;
  justify-content: flex-start;
}

.__wab_root--centered {
  padding: 20px;
  align-items: center;
  justify-content: center;
}

.__wab_root--page-stretch {
  align-items: stretch;
  height: auto;
}

.__wab_root--page-stretch .__wab_val_root:not(.__wab_val_root .__wab_val_root) {
  height: auto !important;
}

.__wab_root--live {
  align-items: center;
  justify-items: center;
}

$checkerboard-color: #f0f2f5;
$checkerboard-size: 16px;
.__wab_root--checkerboard {
  background: linear-gradient(45deg, $checkerboard-color 25%, transparent 25%),
    linear-gradient(-45deg, $checkerboard-color 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, $checkerboard-color 75%),
    linear-gradient(-45deg, transparent 75%, $checkerboard-color 75%), white;
  background-size: $checkerboard-size $checkerboard-size;
  background-position: 0 0, 0 calc($checkerboard-size / 2),
    calc($checkerboard-size / 2) calc(0px - $checkerboard-size / 2),
    calc(0px - $checkerboard-size / 2) 0;
}

@mixin codefont {
  font-family: "IBM Plex Mono", monospace;
}

$grab: url(/static/img/openhand.cur) 10 7, default;
$grabbing: url(/static/img/closedhand.cur) 10 7, default;

.panning-grabbable * {
  cursor: $grab !important;
}

.panning-grabbing * {
  cursor: $grabbing !important;
}

$placeholder-shade-thickness: 2px;
$placeholder-hue: black;

// This is to color the "shade" of slot placeholders.
//
// This is actually pretty close to just coloring them gray,
// but there's a slight transparency that lets through some of
// the underlying color.
//
// The use of two layers of translucent color is so that if
// we're on a white background, a darkening shade will appear.
@mixin placeholder-shade($size) {
  box-shadow: inset 0 0 0 $size rgba(0, 0, 0, 0.1),
    inset 0 0 0 $size rgba(255, 255, 255, 0.8);
}

.__wab_placeholder_border {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  border: 2px dashed rgba(0, 0, 0, 0.5);
}

.__wab_placeholder_border_inner {
  position: relative;
  border: 2px solid rgba(255, 255, 255, 0.5);
  height: 100%;
  width: 100%;
}

.__wab_placeholder {
  position: relative;
  height: 100%;
  width: 100%;
  flex: 1;
  font-family: var(--mixin-default_font-family);
  display: none;
}

.__wab_placeholder__inner {
  height: 100%;
  width: 100%;
}

.__wab_placeholder__inner__autoWidth {
  // Set it to 64.101px so that we can detect if user has an explicit size or
  // not. This is to taking advantage of the precision in Plasmic - one cannot
  // set fractional size explicitly. However, one can still achieve that using
  // %. For example, if parent is 1000px, then setting width to 6.81% can
  // achieve 68.1px. While it is still possible, it should be difficult to get
  // 64.101px (one would need to have both the parent and self to be % based,
  // which should be rare).
  min-width: 64.101px;
}

.__wab_placeholder__inner__autoHeight {
  min-height: 64.101px;
}

.__wab_placeholder__inner__autoHeight--text {
  min-height: 1em;
}

.__wab_error-display {
  border: 2px dashed rgba(255, 255, 255, 0.8);
  border: 2px dashed rgba(255, 0, 0, 1);
  border: none;
  position: relative;
  background: rgba(255, 200, 200, 1);
  background: white;
  background: rgba(200, 90, 90, 1);
  font: 14px Helvetica;
  color: white;
}

.__wab_error-display__inner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border: 4px solid rgba(255, 0, 0, 0.3);
  border: 4px solid rgba(200, 90, 90, 1);
  border: 4px solid rgba(255, 255, 255, 0.3);
  border: $placeholder-shade-thickness solid rgba(0, 0, 0, 0.2);
  min-height: 64px;
  min-width: 64px;
  padding: 8px;
}

.__wab_error-display__heading {
  margin-bottom: 8px;
}

.__wab_error-display__code {
  @include codefont;
}

.__wab_placeholder__tag {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  color: black;
  max-width: 100%;
  align-items: flex-start;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.__wab_editing {
  cursor: text !important;
  outline: none;

  // Target all the elements with texts that can be
  // edited to re-enable the cursor and user selection.
  p,
  span {
    cursor: text !important;
    user-select: text;
  }
}

.__wab_text.__wab_editing {
  white-space: pre-wrap !important;
}

.__wab_canvas_overlay {
  position: absolute;
  top: 0px;
  left: 0px;
  height: 100vh;
  width: 100vw;
  z-index: 1000000000;
  // Some components like radix-ui dialog will disable pointer events on body.
  pointer-events: initial;
}

.__interactive_canvas .__wab_canvas_overlay {
  display: none !important;
}

:where(.__wab_rich_text p) {
  margin: 0;
}

// The following CSS is required to make `list-style-position: inside` work
// properly on canvas. Canvas creates two `div`s inside `li`s and if they
// have `display: block` (which is the default `display` value for `div`)
// they will break a line after the list marker.
:where(li > .__wab_rich_text) {
  display: inline;
  > div {
    display: inline;
  }
}

.__wab_error {
  background: red;
  font: 10px sans-serif;
  color: white;
}

:where(.__wab_text) {
  display: inline-block;
}

.__wab_expr_html_text {
  white-space: normal;
}

.__wab_tag {
  // Needs to be able to interact with an element on canvas even if it has
  // pointer-events set to none.
  pointer-events: auto !important;
}

.__non_interactive_canvas .__wab_tag {
  transition: none !important;
}

.__non_interactive_canvas .__wab_tag::-webkit-scrollbar {
  // We're using a virtual fake scrollbar for scrolling containers,
  // so we hide the native one
  display: none;
}

.__non_interactive_canvas .__wab_instance {
  transition: none !important;
}

:where(.__wab_img_instance) {
  display: inline-block;
}

:where(.__wab_svg) {
  display: inline-block;
}

:where(.__wab_svg > svg) {
  width: 100%;
  height: 100%;
  display: block;
}

.__wab_slot {
  display: contents;
}

.debugger {
  position: fixed;
  padding: 10px;
  width: 250px;
  height: 100%;
  right: 0;
  top: 0;
  display: flex;
  flex-direction: column;
}

.debugger-panel-container {
  position: relative;
  flex: 1;
}

.debugger-toggle-btn {
  position: fixed;
  bottom: 0;
  right: 0;
}

// Hide Google Translate toolbar

.goog-te-banner-frame {
  display: none !important;
}

body {
  top: 0 !important;
}

// Container for implementing the flex gap.  This element lives inside the
// tpl node that has the flex gap.
.__wab_flex-container {
  // We want this element to fill up the dimensions of the parent
  display: flex;
  flex: 1;
  align-self: stretch;

  // Hide pointer-events, because it will have negative margins and so
  // will take up some space outside of its parent.
  pointer-events: none;
}

.__wab_flex-container > * {
  pointer-events: auto;
}

// A positioning wrapper around ValComponents, specifying positioning
// styles like left/top/width/height/margin/etc.
.__wab_component-wrapper {
  // `display: grid` by default stretches its child in both width and height
  display: grid !important;
}

.__wab_component-wrapper--cms-demo {
  display: contents !important;
}

.__wab_inline {
  display: inline !important;
}

nextjs-portal {
  display: none;
}

body > iframe {
  display: none;
}

gatsby-fast-refresh {
  display: none;
}
