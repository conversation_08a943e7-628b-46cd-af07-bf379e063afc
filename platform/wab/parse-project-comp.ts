import * as fs from 'fs';

// Define the TypeScript interfaces based on the data structure
interface Dependency {
  depId: string;
  depProjectId: string;
  depProjectName: string;
  componentsUsed: number;
}

interface ProjectRecord {
  id: string;
  name: string;
  createdAt: string;
  workspaceId: string;
  workspaceName: string;
  teamId: string;
  teamName: string;
  billingEmail: string;
  stripeCustomerId: string;
  seats: number;
  revision: number;
  revisionCreatedAt: string;
  deps: Dependency[];
}

interface Result {
  searchString: string,
  minComponentsUsed: number,
  matches: {
    record: ProjectRecord;
    matchingDependencies: Dependency[];
  }[]
}

class JSONLParser {
  /**
   * Parse a JSONL file and return all records
   */
  static parseFile(filePath: string): ProjectRecord[] {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    return this.parseContent(fileContent);
  }

  /**
   * Parse JSONL content and return all records
   */
  static parseContent(content: string): ProjectRecord[] {
    const lines = content.trim().split('\n');
    const records: ProjectRecord[] = [];

    for (let i = 0; i < lines.length; i++) {
      try {
        const record = JSON.parse(lines[i]) as ProjectRecord;
        records.push(record);
      } catch (error) {
        console.error(`Error parsing line ${i + 1}:`, error);
      }
    }

    return records;
  }

  /**
   * Find records with dependencies containing a specific string in depProjectName
   * and having at least the specified number of components used
   */
  static findRecordsByDependency(
    records: ProjectRecord[],
    searchString: string,
    minComponentsUsed: number = 1
  ): Result {
    const matches: Result['matches'] = [];

    for (const record of records) {
      const matchingDependencies = record.deps.filter(dep => {
        const nameContainsString = dep.depProjectName
          .toLowerCase()
          .includes(searchString.toLowerCase());
        const hasEnoughComponents = dep.componentsUsed >= minComponentsUsed;

        return nameContainsString && hasEnoughComponents;
      });

      if (matchingDependencies.length > 0) {
        matches.push({
          record,
          matchingDependencies
        });
      }
    }

    return {
      searchString,
      minComponentsUsed,
      matches,
    };
  }

  /**
   * Display results in a formatted way
   */
  static displayResults({ searchString, minComponentsUsed, matches }: Result): void {
    console.log(`\n=== Results for "${searchString}" with components used >= ${minComponentsUsed} ===`);
    console.log(`Found ${matches.length} matching records\n`);

    if (matches.length === 0) {
      console.log('No records found matching the criteria.');
      return;
    }

    matches.forEach((result, index) => {
      console.log(`${index + 1}. Record: ${result.record.name}`);
      console.log(`   Project ID: ${result.record.id}`);
      console.log(`   Workspace: ${result.record.workspaceName}`);
      console.log(`   Team: ${result.record.teamName}`);
      console.log(`   Matching dependencies:`);

      result.matchingDependencies.forEach((dep, depIndex) => {
        console.log(`     ${depIndex + 1}. ${dep.depProjectName}`);
        console.log(`        Components Used: ${dep.componentsUsed}`);
        console.log(`        Dep ID: ${dep.depId}`);
      });
      console.log('');
    });
  }

  /**
   * Display results in CSV format with headers
   */
  static displayResultsCSV({ searchString, minComponentsUsed, matches }: Result): void {
    // Print CSV header
    console.log('ProjectName,ProjectId,WorkspaceName,TeamName,BillingEmail,StripeCustomerId,Seats,DependencyName,ComponentsUsed,DependencyId,DependencyProjectId');

    if (matches.length === 0) {
      return;
    }

    // Print each matching record with its dependencies
    matches.forEach((result) => {
      const record = result.record;

      result.matchingDependencies.forEach((dep) => {
        // Escape CSV values that might contain commas or quotes
        const escapeCSV = (value: string | number): string => {
          const str = String(value);
          if (str.includes(',') || str.includes('"') || str.includes('\n')) {
            return `"${str.replace(/"/g, '""')}"`;
          }
          return str;
        };

        const csvRow = [
          escapeCSV(record.name),
          escapeCSV(record.id),
          escapeCSV(record.workspaceName),
          escapeCSV(record.teamName),
          escapeCSV(record.billingEmail),
          escapeCSV(record.stripeCustomerId),
          escapeCSV(record.seats),
          escapeCSV(dep.depProjectName),
          escapeCSV(dep.componentsUsed),
          escapeCSV(dep.depId),
          escapeCSV(dep.depProjectId)
        ].join(',');

        console.log(csvRow);
      });
    });
  }

  /**
   * Get statistics about the dataset
   */
  static getStatistics(records: ProjectRecord[]): void {
    console.log('\n=== Dataset Statistics ===');
    console.log(`Total records: ${records.length}`);

    const totalDependencies = records.reduce((sum, record) => sum + record.deps.length, 0);
    console.log(`Total dependencies: ${totalDependencies}`);

    const dependenciesWithComponents = records.reduce((sum, record) => {
      return sum + record.deps.filter(dep => dep.componentsUsed > 0).length;
    }, 0);
    console.log(`Dependencies with components used > 0: ${dependenciesWithComponents}`);

    // Get unique dependency project names
    const uniqueDepNames = new Set<string>();
    records.forEach(record => {
      record.deps.forEach(dep => {
        uniqueDepNames.add(dep.depProjectName);
      });
    });
    console.log(`Unique dependency project names: ${uniqueDepNames.size}`);
  }
}

// Usage example
async function main() {
  try {
    // Parse the file
    const records = JSONLParser.parseFile('project-comp-count.txt');

    // Get statistics
    JSONLParser.getStatistics(records);

    // Search for records with "commerce" dependencies that have at least 1 component used
    const commerceResults = JSONLParser.findRecordsByDependency(records, 'commerce', 2);
    JSONLParser.displayResults(commerceResults);
  } catch (error) {
    console.error('Error processing file:', error);
  }
}

// Run the main function if this file is executed directly
if (require.main === module) {
  main();
}
