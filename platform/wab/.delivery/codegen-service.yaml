apiVersion: v1
kind: Service
metadata:
  name: codegen-service-heavy-ops
  namespace: default
spec:
  internalTrafficPolicy: Cluster
  ports:
  - name: http
    port: 80
    protocol: TCP
    targetPort: 3004
  selector:
    app: codegen-pod
  sessionAffinity: None
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: codegen-service-light-ops
  namespace: default
spec:
  internalTrafficPolicy: Cluster
  ports:
  - name: http
    port: 80
    protocol: TCP
    targetPort: 3004
  selector:
    app: codegen-light-pod
  sessionAffinity: None
  type: ClusterIP
