<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="24px" height="20px" viewBox="0 0 24 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.1 (35376) - http://www.bohemiancoding.com/sketch -->
    <title>icons/flex-hbetween</title>
    <desc>Created with Sketch.</desc>
    <defs></defs>
    <g id="Flexbox" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Container" transform="translate(-175.000000, -228.000000)">
            <g id="icons/flex-hbetween" transform="translate(187.000000, 238.000000) rotate(-90.000000) translate(-187.000000, -238.000000) translate(177.000000, 225.000000)">
                <path d="M0.5,1.5 L19.5,1.5" id="Line-Copy-7" stroke-opacity="0.52173913" stroke="#979797" fill-opacity="0.5" fill="#979797"></path>
                <path d="M0.5,24.5 L19.5,24.5" id="Line-Copy-6" stroke-opacity="0.52173913" stroke="#979797" fill-opacity="0.5" fill="#979797"></path>
                <rect id="Rectangle-Copy-7" fill="#888C90" x="6" y="3" width="8" height="3"></rect>
                <rect id="Rectangle-Copy-6" fill="#888C90" x="6" y="20" width="8" height="3"></rect>
            </g>
        </g>
    </g>
</svg>