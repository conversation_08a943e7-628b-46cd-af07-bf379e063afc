<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="33px" height="29px" viewBox="0 0 33 29" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.2 (35397) - http://www.bohemiancoding.com/sketch -->
    <title>columns</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0.639029154" y="0.5" width="14.3059737" height="28"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14.3059737" height="28" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <rect id="path-3" x="18.0549971" y="0.5" width="14.3059737" height="28"></rect>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14.3059737" height="28" fill="white">
            <use xlink:href="#path-3"></use>
        </mask>
    </defs>
    <g id="All-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-dasharray="2,2,2,2">
        <g id="Create-Sidebar" transform="translate(-44.000000, -209.000000)" stroke="#979797" stroke-width="2">
            <g id="columns" transform="translate(44.000000, 209.000000)">
                <use id="Rectangle-3" mask="url(#mask-2)" xlink:href="#path-1"></use>
                <use id="Rectangle-3" mask="url(#mask-4)" xlink:href="#path-3"></use>
            </g>
        </g>
    </g>
</svg>