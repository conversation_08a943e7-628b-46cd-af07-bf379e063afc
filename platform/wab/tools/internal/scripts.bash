#!/usr/bin/env bash

set -o errexit -o nounset

: $PLASMIC_REPOS_ROOT

notify() {
  type osascript &> /dev/null && osascript -e "display notification \"$@\"" || true
}

local-publish-hostless() {
  cd $PLASMIC_REPOS_ROOT/public-packages &&
  : yarn local-publish @plasmicapp/react-ssr-prepass &&
  yarn local-publish @plasmicapp/host &&
  yarn local-publish @plasmicapp/loader-nextjs &&
  yarn local-publish @plasmicpkgs/antd5 &&
  yarn local-publish @plasmicpkgs/plasmic-rich-components &&
  notify "PUBLISHED!!!"
}

local-install-host-dev() {
  local-publish-hostless &&
  z x121 &&
  rm -rf .next node_modules package-lock.json &&
  local-npm i &&
  yarn dev
}

local-install-hostless() {
  local-publish-hostless &&
  cd $PLASMIC_REPOS_ROOT/plasmic/canvas-packages &&
  local-yarn add @plasmicpkgs/antd5 @plasmicpkgs/plasmic-rich-components &&
  yarn build &&
  cd $PLASMIC_REPOS_ROOT/plasmic/wab/ &&
  local-yarn add -W @plasmicpkgs/antd5 @plasmicpkgs/plasmic-rich-components &&
  notify "READY FOR TESTING!!!" &&
  yarn db:projects upgrade-hostless-deps -p PROJECT_ID &&
  cd $PLASMIC_REPOS_ROOT/plasmic/loader-bundle-env/ &&
  local-yarn add @plasmicpkgs/antd5 @plasmicpkgs/plasmic-rich-components &&
  notify "ALL DONE!!!"
}

upgrade-internal() {
  cd $PLASMIC_REPOS_ROOT/plasmic/ &&
  { [[ -z "$(git status --porcelain)" ]] || { echo 'Dirty' && git status && return 1 ; } ; } &&
  git pull &&
  yarn upgrade-internal &&
  git commit -am 'Upgrade internal' &&
  git review -y
  notify "ALL DONE!!!"
}

add-hostless-package() {
  cd $PLASMIC_REPOS_ROOT/plasmic/canvas-packages &&
  yarn add "$@" &&
  cd $PLASMIC_REPOS_ROOT/plasmic/loader-bundle-env &&
  yarn add "$@" &&
  cd $PLASMIC_REPOS_ROOT/plasmic/wab &&
  yarn add -W "$@" &&
  cat > $PLASMIC_REPOS_ROOT/plasmic/canvas-packages/src/"${@##*/}".ts << EOF
import { registerAll } from "$@";

export function register() {
  registerAll();
}

register();
EOF
  sed -i '' "s/\n\]/,  \"${@##*/}\"\n]/" canvas-packages/hostlessList.json
  sed -i '' "s/\]/  \"@plasmicpkgs\\/${@##*/}\"\n]/" wab/src/wab/server/loader/hostless-packages.ts
  sed -i '' "s/\]/  \"${@##*/}\"\n]/" wab/src/wab/hostless-components.ts
}

get-secrets-json() {
  kubectl get secret prod-secrets -o "jsonpath={.data.secrets\.json}" |
  base64 -d
}

edit-secrets-json() {
  get-secrets-json |
  vipe |
  kubectl create secret generic secrets.json --dry-run=client --from-file=secrets.json=/dev/stdin -o json |
  kubeseal --merge-into $PLASMIC_REPOS_ROOT/platform/wab/.delivery/prod-secrets-sealed.yaml -o yaml
}

"$@"
