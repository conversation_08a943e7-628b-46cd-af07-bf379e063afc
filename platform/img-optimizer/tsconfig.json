{"compilerOptions": {"target": "ES2017", "module": "CommonJS", "moduleResolution": "Node", "baseUrl": "./", "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "noImplicitAny": true, "noEmit": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitThis": true, "skipLibCheck": true, "strict": true, "strictFunctionTypes": true, "strictNullChecks": true}, "include": ["src"]}