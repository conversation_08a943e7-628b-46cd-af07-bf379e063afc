apiVersion: apps/v1
kind: Deployment
metadata:
  name: img-optimizer-deployment
spec:
  selector:
    matchLabels:
      app: img-optimizer-pod
  template:
    metadata:
      labels:
        app: img-optimizer-pod
      annotations:
        prometheus.io/scrape: 'true'
    spec:
      containers:
        - name: img-optimizer
          image: 939375546786.dkr.ecr.us-west-2.amazonaws.com/img-optimizer:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 3009
          readinessProbe:
            tcpSocket:
              port: 3009
            initialDelaySeconds: 5
            periodSeconds: 5
          resources:
            requests:
              cpu: "300m"
              memory: "1Gi"
            limits:
              memory: "1Gi"
# Add this back in if we want to force these to run on an "untrusted" node group.
#      nodeSelector:
#        alpha.eksctl.io/nodegroup-name: untrusted
#      tolerations:
#        - key: "nodegroup"
#          operator: "Equal"
#          value: "untrusted"
#          effect: "NoSchedule"
