export class CustomError extends Error {
  name: string;
  constructor(msg?: string) {
    super(msg);
    // doesn't quite work; from <https://github.com/jaekwon/lessons/blob/master/coffeescript/errors.coffee>
    this.name = this.constructor.name;
    ({ message: this.message, stack: this.stack } = this);
  }
}

export class AssertionError extends CustomError {
  constructor(msg = "Asser<PERSON> failed") {
    super(msg);
  }
}

export class ApiError extends Error {
  name = "ApiError";
  statusCode: number;

  constructor(message: string, statusCode?: number) {
    super(message);

    // ensure an error status is used > 400
    if (statusCode && statusCode >= 400) {
      this.statusCode = statusCode;
    } else {
      this.statusCode = 500;
    }
  }
}

export class BadRequestError extends ApiError {
  name = "BadRequestError";

  constructor(message: string, statusCode: number = 400) {
    super(message, statusCode);
  }
}
