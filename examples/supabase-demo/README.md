<p align="center">
  <a href="https://www.plasmic.app">
    <img alt="Plasmic" role="img" src="https://cdn-images-1.medium.com/max/176/1*<EMAIL>" width="120">
  </a>
</p>

# Plasmic+Supabase Demo

This is a demo of building a Pokedex on top of Plasmic (for frontend), Supabase (for backend), and Next.js (platform). To read more about how this works, check out our tutorial [here](https://supabase.com/docs/guides/integrations/plasmic).

## Getting Started

1. Copy `.env.example` to `.env` and populate your Supabase credentials.
   The tutorial above will show you how to properly set up a compatible Supabase project.
2. Run `yarn` to install dependencies, fetch the schema and types from supabase.

3. Run the development server:

```bash
yarn dev
```

4. Open your browser to see the result.

5. You can view the Plasmic-built frontend [here](https://studio.plasmic.app/projects/i6YZmFxPJB69NEUgdf79ap). If you want to make edits, clone the Plasmic project and note the new
   project ID and token. These fields will need to be updated in `.env` to reflect your changes.

## Learn More

With Plasmic, you can enable non-developers on your team to publish pages and content into your website or app.

To learn more about Plasmic, take a look at the following resources:

- [Plasmic Website](https://www.plasmic.app/)
- [Plasmic Documentation](https://docs.plasmic.app/learn/)
- [Plasmic Community Forum](https://forum.plasmic.app/)

You can check out [the Plasmic GitHub repository](https://github.com/plasmicapp/plasmic) - your feedback and contributions are welcome!

Note: This Next.js project was bootstrapped with [`create-plasmic-app`](https://www.npmjs.com/package/create-plasmic-app).
