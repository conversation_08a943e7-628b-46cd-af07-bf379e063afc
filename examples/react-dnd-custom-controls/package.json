{"name": "tmp-cpa", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@plasmicapp/loader-nextjs": "^1.0.158", "immutability-helper": "^3.1.1", "next": "12.2.2", "react": "18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "18.2.0"}, "devDependencies": {"@types/node": "18.0.6", "@types/react": "18.0.15", "@types/react-dom": "18.0.6", "eslint": "8.20.0", "eslint-config-next": "12.2.2", "typescript": "4.7.4"}}