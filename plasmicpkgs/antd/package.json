{"name": "@plasmicpkgs/antd", "version": "2.0.141", "description": "Plasmic registration calls for antd components", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/antd.esm.js", "nx": {"targets": {"build": {"inputs": ["{projectRoot}/**/*", "!{projectRoot}/**/dist/**/*", "!{projectRoot}/skinny/**/*"], "outputs": ["{projectRoot}/**/dist/**/*", "{projectRoot}/skinny/**/*"]}}}, "exports": {".": {"require": "./dist/index.js", "import": "./dist/antd.esm.js", "types": "./dist/index.d.ts"}, "./dist/antd.css": "./dist/antd.css", "./skinny/*": {"require": "./skinny/*.cjs.js", "import": "./skinny/*.esm.js", "types": "./skinny/*.d.ts"}}, "files": ["dist", "skinny"], "size-limit": [{"path": "dist/antd.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/antd.esm.js", "limit": "10 KB"}], "scripts": {"build": "rollup -c rollup.config.mjs && yarn copy_css_files && yarn gentypes", "gentypes": "yarn tsc --emitDeclarationOnly --declaration src/index.ts --incremental --tsBuildInfoFile ./dist/.tsbuildinfo  --skipLibCheck --jsx react --lib dom,es2019 --esModuleInterop --strict --outDir ./dist/ && cp ./dist/*.d.ts skinny/ && rm skinny/index.d.ts", "prepublishOnly": "npm run build", "copy_css_files": "cp src/*.css dist/", "size": "size-limit", "analyze": "size-limit --why"}, "dependencies": {"antd": "^4.19.5"}, "devDependencies": {"@plasmicapp/host": "1.0.219", "@rollup/plugin-commonjs": "^11.0.0", "@rollup/plugin-json": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "@size-limit/preset-small-lib": "^4.11.0", "@types/glob": "^7.1.3", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "glob": "^7.1.3", "rc-menu": "~9.8.0", "rc-select": "~14.1.17", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^3.10.1", "rollup-plugin-esbuild": "^5.0.0", "rollup-plugin-replace-imports": "^1.0.0", "size-limit": "^4.11.0", "typescript": "^5.2.2"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0"}}