{"name": "@plasmicpkgs/framer-motion", "version": "0.0.217", "description": "Plasmic registration call for Framer Motion", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/framer-motion.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/framer-motion.cjs.production.min.js", "limit": "50 KB"}, {"path": "dist/framer-motion.esm.js", "limit": "50 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.219", "@size-limit/preset-small-lib": "^7.0.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "size-limit": "^7.0.0", "tsdx": "^0.14.1", "tslib": "^2.2.0"}, "dependencies": {"framer-motion": "^5.3.0"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}}