{"name": "@plasmicpkgs/plasmic-rich-components", "version": "1.0.215", "description": "Rich batteries-included general purpose components for business apps, admin panels, etc.", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/plasmic-rich-components.esm.js", "files": ["dist", "skinny"], "exports": {".": {"require": "./dist/index.js", "import": "./dist/plasmic-rich-components.esm.js", "types": "./dist/index.d.ts"}, "./skinny/*": {"require": "./skinny/*/index.cjs.js", "import": "./skinny/*/index.esm.js", "types": "./skinny/*/index.d.ts"}}, "size-limit": [{"path": "dist/plasmic-rich-components.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/plasmic-rich-components.esm.js", "limit": "10 KB"}], "scripts": {"build": "rollup -c rollup.config.mjs && yarn tsc --emitDeclarationOnly --declaration src/index.tsx --incremental --tsBuildInfoFile ./dist/.tsbuildinfo  --skipLibCheck --jsx react --lib dom,esnext --esModuleInterop --strict --outDir ./dist/ && rsync -r --include='*/' --include='*.d.ts' --exclude='*' ./dist/ ./skinny/ && rm skinny/index.d.ts", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependenciesComments": {"@ant-design/pro-components": "Must be 2.6.4. Earlier doesn't support newer antd, later incurs a disallowed dynamic require of antd/es/layout/Sider. https://app.shortcut.com/plasmic/story/37043/richlayout-always-initially-loads-with-dark-background"}, "devDependencies": {"@ant-design/icons": "^5.0.1", "@ant-design/pro-components": "2.6.4", "@plasmicapp/data-sources": "0.1.183", "@plasmicapp/host": "1.0.219", "@rollup/plugin-commonjs": "^25.0.2", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.1.0", "@size-limit/preset-small-lib": "^4.11.0", "@types/lodash": "^4.14.200", "@types/node": "^20.8.9", "@types/react": "^18.2.33", "antd": "^5.4.0", "glob": "^8.1.0", "rollup": "^3.26.2", "rollup-plugin-esbuild": "^5.0.0", "size-limit": "^4.11.0", "tslib": "^2.2.0", "typescript": "^5.2.2"}, "peerDependencies": {"@ant-design/icons": ">=5.0.0", "@ant-design/pro-components": "2.6.4", "@plasmicapp/data-sources": ">=0.1.52", "@plasmicapp/host": ">=1.0.0", "antd": ">=5.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "publishConfig": {"access": "public"}, "dependencies": {"@ctrl/tinycolor": "^3.6.1", "@plasmicpkgs/luxon-parser": "^3.4.4", "classnames": "^2.3.2", "csv-writer-browser": "^1.0.0", "dayjs": "^1.11.10", "fast-stringify": "^2.0.0", "lodash": "^4.17.21"}}