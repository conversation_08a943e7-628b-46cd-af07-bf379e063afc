{"name": "@plasmicpkgs/plasmic-strapi", "version": "0.1.169", "description": "Plasmic Strapi components.", "main": "dist/index.js", "typings": "dist/index.d.ts", "module": "dist/index.mjs", "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"build": "tsup-node src/index.tsx --dts --format esm,cjs --target es2019", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "peerDependencies": {"react": ">=16.8.0"}, "size-limit": [{"path": "dist/index.js", "limit": "10 KB"}, {"path": "dist/index.mjs", "limit": "10 KB"}], "dependencies": {"@types/dlv": "^1.1.2", "change-case": "^4.1.2", "dlv": "^1.1.3", "qs": "^6.11.0"}, "devDependencies": {"@plasmicapp/host": "1.0.219", "@plasmicapp/query": "0.1.79", "@size-limit/preset-small-lib": "^7.0.8", "@types/dlv": "^1.1.2", "@types/qs": "^6.9.7", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "husky": "^7.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "size-limit": "^7.0.8", "tslib": "^2.3.1", "tsup": "^7.2.0", "typescript": "^5.2.2"}}