{"version": "0.0.60", "license": "MIT", "main": "dist/index.js", "typings": "dist/index.d.ts", "publishConfig": {"access": "public"}, "files": ["dist"], "engines": {"node": ">=10"}, "scripts": {"start": "tsdx watch", "build": "tsdx build", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "peerDependencies": {"react": ">=16"}, "husky": {"hooks": {"pre-commit": "tsdx lint"}}, "prettier": {"printWidth": 80, "semi": true, "singleQuote": true, "trailingComma": "es5"}, "name": "@plasmicpkgs/plasmic-tabs", "author": "<PERSON><PERSON><PERSON><PERSON>", "module": "dist/plasmic-tabs.esm.js", "size-limit": [{"path": "dist/plasmic-tabs.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/plasmic-tabs.esm.js", "limit": "10 KB"}], "devDependencies": {"@size-limit/preset-small-lib": "^7.0.4", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "husky": "^7.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "size-limit": "^7.0.4", "tsdx": "^0.14.1", "tslib": "^2.3.1"}, "dependencies": {"@plasmicapp/host": "1.0.219", "constate": "^3.3.2"}}