{"name": "@plasmicpkgs/react-parallax-tilt", "version": "0.0.219", "description": "Plasmic registration call for the HTML5 video element", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/react-parallax-tilt.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/react-parallax-tilt.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/react-parallax-tilt.esm.js", "limit": "10 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.219", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "size-limit": "^4.11.0", "tsdx": "^0.14.1", "tslib": "^2.2.0"}, "dependencies": {"react-parallax-tilt": "^1.5.74"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}}