[{"scope": "https://next-js-store.myshopify.com:443", "method": "POST", "path": "/api/2025-01/graphql.json", "body": {"query": "\n    query getSiteCollections($first: Int!) {\n  collections(first: $first) {\n    edges {\n      node {\n        ...collection\n        products(first: $first) {\n          edges {\n            node {\n              id\n            }\n          }\n        }\n      }\n    }\n  }\n}\n    fragment collection on Collection {\n  id\n  title\n  handle\n  image {\n    ...image\n  }\n}\nfragment image on Image {\n  url\n  altText\n  width\n  height\n}", "variables": {"first": 250}}, "status": 200, "response": ["1f8b0800000000000013c591b16ac33010865fa5dc6c907439498eb652e818ba970ec2bad802d50ab69c528cdfbdb8a54d3a359e3adc703f7cf0df7d33045f3cb8199a9c123725e67e5c570e2d8fe09e67e873e03589011cb4313821c62e9fe2f15d3cfc3002ad24855649a9112a28b1240607077ebbbb1f8678f669840a3adf87cfbc49b974bc46f1d5b70cae9f52aae034e43035e5f6064f5f80300695b56888b432b02cd54d9426b23bd41289506ea4c8e25ecb7a2b85565abdb5e1ae562825c1b2bc2c7f91bf9c28544a19bb97f5959347f6651a385cfb385eb2ff1772397713a5b431f4fda4753e0059e6743ddd020000"], "rawHeaders": {"access-control-allow-origin": "*", "alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "90c2e4a9990cf7d7-LAX", "connection": "keep-alive", "content-encoding": "gzip", "content-language": "en", "content-security-policy": "block-all-mixed-content; frame-ancestors 'none'; upgrade-insecure-requests;", "content-type": "application/json; charset=utf-8", "date": "Mon, 03 Feb 2025 13:51:00 GMT", "nel": "{\"success_fraction\":0.01,\"report_to\":\"cf-nel\",\"max_age\":604800}", "powered-by": "Shopify", "report-to": "{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=ILk6Q1xVjuaqYqW%2BrUo3xRs2PoqjDJDMgJQBpv3XuL6IoSk4DgprsCH%2BGUlOJPC8TWE3U5afo5JlpMjS0lAHWH6HfoA37yEUPcQgfO306Sfs6GVqE%2F6r9rt%2FhhfundpIb2OZU2pLJHqzukTxnA%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}", "server": "cloudflare", "server-timing": "processing;dur=11;desc=\"gc:1\", db;dur=2, asn;desc=\"7922\", edge;desc=\"LAX\", country;desc=\"US\", servedBy;desc=\"kflj\", graphql;desc=\"storefront/query/getSiteCollections\", gqlSelectionNames;desc=\"sfr/collections\", requestID;desc=\"e373812c-6c95-4994-9446-bc7d522c5718-1738590660\", cfRequestDuration;dur=60.999870", "transfer-encoding": "chunked", "vary": "Accept-Encoding,Accept", "x-cache": "hit, server", "x-content-type-options": "nosniff", "x-dc": "gcp-us-west1,gcp-us-west1,gcp-us-west1", "x-download-options": "noopen", "x-frame-options": "DENY", "x-permitted-cross-domain-policies": "none", "x-request-id": "e373812c-6c95-4994-9446-bc7d522c5718-1738590660", "x-shardid": "163", "x-shopid": "43402854564", "x-shopify-api-version": "2025-01", "x-sorting-hat-podid": "163", "x-sorting-hat-shopid": "43402854564", "x-storefront-renderer-rendered": "1", "x-xss-protection": "1; mode=block"}, "responseIsBinary": false}]