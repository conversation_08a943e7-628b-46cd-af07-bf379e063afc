{"name": "@plasmicpkgs/commerce", "version": "0.0.217", "description": "Plasmic registration calls for commerce components", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/commerce.esm.js", "files": ["dist"], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.219", "@plasmicapp/query": "0.1.79", "@size-limit/preset-small-lib": "^4.11.0", "@types/debounce": "^1.2.1", "@types/js-cookie": "^3.0.1", "@types/lodash.debounce": "^4.0.7", "@types/node": "^14.0.26", "react": "^18.2.0", "react-dom": "^18.2.0", "size-limit": "^4.11.0", "tsdx": "^0.14.1"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "@plasmicapp/query": ">=0.1.0", "react": ">=16.8.0"}, "dependencies": {"@types/react": "^18.0.27", "@vercel/fetch": "^6.2.0", "debounce": "^1.2.1", "js-cookie": "^3.0.1", "lodash.debounce": "^4.0.8", "react-hook-form": "^7.28.0", "swr": "^1.2.2"}}