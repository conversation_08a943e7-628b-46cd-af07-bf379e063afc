{"name": "@plasmicpkgs/commerce-saleor", "version": "0.0.181", "description": "Plasmic registration calls for saleor commerce provider", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/commerce-saleor.esm.js", "files": ["dist"], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "dependencies": {"@plasmicpkgs/commerce": "0.0.217", "debounce": "^1.2.1", "js-cookie": "^3.0.5"}, "peerDependencies": {"next": "^12", "react": ">=16.8.0"}, "devDependencies": {"@plasmicapp/host": "1.0.219", "@size-limit/preset-small-lib": "^7.0.8", "@types/debounce": "^1.2.1", "@types/js-cookie": "^3.0.5", "@types/node": "^17.0.8", "@types/react": "^18.0.27", "next": "^12.0.8", "prettier": "^2.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "size-limit": "^7.0.8", "tsdx": "^0.14.1"}, "publishConfig": {"access": "public"}}