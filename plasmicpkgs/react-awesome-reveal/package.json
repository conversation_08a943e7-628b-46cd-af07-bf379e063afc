{"name": "@plasmicpkgs/react-awesome-reveal", "version": "3.8.221", "description": "Plasmic registration call for react-awesome-reveal", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/react-awesome-reveal.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/react-awesome-reveal.cjs.production.min.js", "limit": "15 KB"}, {"path": "dist/react-awesome-reveal.esm.js", "limit": "15 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.219", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "size-limit": "^4.11.0", "tsdx": "^0.14.1", "tslib": "^2.2.0"}, "dependencies": {"@emotion/react": "^11.11.4", "react-awesome-reveal": "^4.2.12"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}}