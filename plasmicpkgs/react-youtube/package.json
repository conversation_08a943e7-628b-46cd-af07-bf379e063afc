{"name": "@plasmicpkgs/react-youtube", "version": "7.13.223", "description": "Plasmic registration call for react-youtube", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/react-youtube.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/react-youtube.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/react-youtube.esm.js", "limit": "10 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.219", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "@types/youtube-player": "^5.5.6", "size-limit": "^4.11.0", "tsdx": "^0.14.1", "tslib": "^2.2.0"}, "dependencies": {"react-youtube": "9.0.2"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}}