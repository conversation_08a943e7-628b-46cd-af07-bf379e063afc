{"name": "@plasmicpkgs/plasmic-cms", "version": "0.0.284", "description": "Plasmic CMS components", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/plasmic-cms.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/plasmic-cms.cjs.production.min.js", "limit": "20 KB"}, {"path": "dist/plasmic-cms.esm.js", "limit": "20 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.219", "@plasmicapp/query": "0.1.79", "@size-limit/preset-small-lib": "^7.0.8", "@types/node": "^17.0.14", "@types/react": "^18.2.33", "size-limit": "^7.0.8", "tsdx": "^0.14.1", "tslib": "^2.3.1"}, "dependencies": {"dayjs": "^1.10.7"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "@plasmicapp/query": ">=0.1.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}}