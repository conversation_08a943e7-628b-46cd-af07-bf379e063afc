{"name": "@plasmicpkgs/radix-ui", "version": "0.0.77", "description": "Radix UI components for Plasmic", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.mjs", "files": ["dist"], "size-limit": [{"path": "dist/index.js", "limit": "10 KB"}, {"path": "dist/index.mjs", "limit": "10 KB"}], "scripts": {"build": "tsup-node src/index.tsx --dts --format esm,cjs --target es2019", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.219", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "size-limit": "^4.11.0", "tslib": "^2.2.0", "tsup": "^7.2.0", "typescript": "^5.2.2"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "publishConfig": {"access": "public"}, "dependencies": {"@radix-ui/react-context-menu": "^2.1.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-popper": "^1.1.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.279.0", "remeda": "^1.27.0"}}