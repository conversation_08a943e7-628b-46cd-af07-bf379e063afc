{"name": "@plasmicpkgs/plasmic-chakra-ui", "version": "0.0.49", "description": "Plasmic registration calls for chakra ui components.", "main": "dist/index.js", "typings": "dist/index.d.ts", "module": "dist/plasmic-chakra-ui.esm.js", "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"start": "tsdx watch", "build": "rollup -c rollup.config.mjs && yarn tsc --emitDeclarationOnly --declaration src/index.ts --incremental --tsBuildInfoFile ./dist/.tsbuildinfo  --skipLibCheck --jsx react --esModuleInterop --strict --outDir ./dist/", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16"}, "size-limit": [{"path": "dist/plasmic-chakra-ui.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/plasmic-chakra-ui.esm.js", "limit": "10 KB"}], "devDependencies": {"@plasmicapp/host": "1.0.219", "@rollup/plugin-commonjs": "^25.0.2", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.1.0", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "@types/react-dom": "^18.2.14", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^3.26.2", "rollup-plugin-esbuild": "^5.0.0", "size-limit": "^4.11.0", "tsdx": "^0.14.1", "tslib": "^2.6.2", "typescript": "^5.2.2"}, "dependencies": {"@chakra-ui/react": "^2.8.1"}}