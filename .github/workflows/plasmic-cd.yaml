name: Plasmic CD
on:
  push:
    branches:
      - master
    paths:
      - platform/**
      - plasmicpkgs/**
      - packages/**
  workflow_dispatch:
    inputs:
      force:
        default: false
        description: "Force deploy even if we detect the built assets already existing"
        required: false
      force-bump:
        description: "Force bump"
        default: false

# Limit to 1 at a time on master (no effect for CRs)
concurrency:
  group: plasmic-cd-${{ github.ref }}

permissions:
  contents: write
jobs:
  plasmic-cd:
    runs-on: arc-runner-set-4cpus-16gb
    timeout-minutes: 90
    # Normally, only run this on master.
    # But if you want to test it out without committing, set this to true (and set plasmic-ci to false).
    # You can optionally set PRETEND_DEPLOY to false as well if you want to do a "real run."
    # if: ${{ github.ref == 'refs/heads/master' }}
    env:
      FORCE: ${{ inputs.force }}
      PRETEND_DEPLOY: ${{ github.ref != 'refs/heads/master' }}
#      PRETEND_DEPLOY: false
      ON_MASTER: ${{ github.ref == 'refs/heads/master' }}
      BUILD_NUMBER: ${{ github.run_id }}
      BUILD_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
      GITHUB_REF: ${{ github.ref }}
      GITHUB_SHA: ${{ github.sha }}
      SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
      NOSUDO: 1
    steps:
      - name: Setup tmate session
        if: false
        uses: mxschmitt/action-tmate@v3
        with:
          detached: true
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2
      - name: Extract branch name
        shell: bash
        run: |
          set -ex
          echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT
          echo "head_ref=${GITHUB_HEAD_REF:-${GITHUB_REF}}" >> $GITHUB_OUTPUT
          echo "ref=${GITHUB_REF}" >> $GITHUB_OUTPUT
          TAG=${GITHUB_SHA:0:6}
          # If we've already deployed this latest commit, then skip. This is the case if we have had multiple pushes queued up near the same time, and they were already all deployed together in a prior run.
          if [[ ! $FORCE ]] && aws s3 ls studio.plasmic.app/versions/$TAG; then
            echo "Already deployed $TAG"
            exit 1
          fi
        id: extract_branch
      - name: Get Github App Token
        uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.PLASMIC_WORKFLOW_APP_ID }}
          private-key: ${{ secrets.PLASMIC_WORKFLOW_SECRET }}
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
          token: ${{ steps.app-token.outputs.token }}
      - name: Setup npm auth
        run: echo //registry.npmjs.org/:_authToken=$NPM_TOKEN > ~/.npmrc
        env:
          NPM_TOKEN: ${{secrets.NPM_TOKEN}}
      - name: Remove IPv6 localhost from /etc/hosts
        run: |
          # Cannot directly sed -i the file since it is managed by Docker, need to write into it instead of moving it
          cat /etc/hosts | grep -v '::1' | tee /tmp/hosts.new
          sudo cp -f /tmp/hosts.new /etc/hosts
      - run: |
          echo "$SENTRYCLIRC" > ~/.sentryclirc
        env:
          SENTRYCLIRC: ${{ secrets.SENTRYCLIRC }}
      - uses: actions/setup-python@v4
        with:
          python-version: 3.11
      - uses: actions/setup-node@v3
        with:
          node-version: 18.19.0
      - name: Get yarn cache directory
        id: yarn-cache-dir-path
        run: npm i -g yarn && echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT
      - run: bash cicd/cicd.bash build_and_deploy
        id: main-step
        env:
          FORCE_BUMP: ${{ github.event.inputs.force-bump }}
          SKIP_UPDATE_HOSTLESS: false
          DIRECT_PUSH_HOSTLESS_MIGRATIONS: true
          SKIP_PACKAGES: false
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          WAB_DBPASSWORD: ${{ secrets.PROD_DB_PASSWORD }}
          K8S: 1
          BASEDIR: ${{ steps.checkout.outputs.basedir }}
      - run: bash cicd/cicd.bash notify-slack "🚨 Deployment failed <!channel> $BUILD_URL"
        if: failure()
