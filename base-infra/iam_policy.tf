resource "aws_iam_policy" "AWSChatbotNotificationsOnly_Policy" {
  description = "NotificationsOnly policy for AWS-Chatbot"
  name        = "AWS-Chatbot-NotificationsOnly-Policy-b1302919-2d5f-4a07-b53e-5b2941bd927f"
  path        = "/service-role/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "cloudwatch:Describe*",
        "cloudwatch:Get*",
        "cloudwatch:List*"
      ],
      "Effect": "Allow",
      "Resource": "*"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "AllowEC2InstanceConnect_Policy" {
  name = "AllowEC2InstanceConnect"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": "ec2-instance-connect:SendSSHPublicKey",
      "Condition": {
        "StringEquals": {
          "ec2:osuser": "ec2-user"
        }
      },
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": "ec2-instance-connect:SendSSHPublic<PERSON><PERSON>",
      "Condition": {
        "StringEquals": {
          "ec2:osuser": "ubuntu"
        }
      },
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": "ec2:DescribeInstances",
      "Effect": "Allow",
      "Resource": "*"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "AllowEC2InstanceConnectAbdulDev_Policy" {
  name = "AllowEC2InstanceConnectAbdulDev"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": "ec2-instance-connect:SendSSHPublicKey",
      "Condition": {
        "StringEquals": {
          "ec2:osuser": "ubuntu"
        }
      },
      "Effect": "Allow",
      "Resource": [
        "arn:aws:ec2:us-west-2:939375546786:instance/i-09a30be3fc5092ddd"
      ]
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "AllowEC2InstanceConnectAll_Policy" {
  name = "AllowEC2InstanceConnectAll"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": "ec2-instance-connect:SendSSHPublicKey",
      "Condition": {
        "StringEquals": {
          "ec2:osuser": "ec2-user"
        }
      },
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": "ec2-instance-connect:SendSSHPublicKey",
      "Condition": {
        "StringEquals": {
          "ec2:osuser": "ubuntu"
        }
      },
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": "ec2:DescribeInstances",
      "Effect": "Allow",
      "Resource": "*"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "AmazonEKSAdminPolicy_Policy" {
  name = "AmazonEKSAdminPolicy"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": "eks:*",
      "Effect": "Allow",
      "Resource": "*",
      "Sid": "VisualEditor0"
    },
    {
      "Action": [
        "iam:ChangePassword"
      ],
      "Effect": "Allow",
      "Resource": [
        "arn:aws:iam::*:user/$${aws:username}"
      ]
    },
    {
      "Action": [
        "iam:GetAccountPasswordPolicy"
      ],
      "Effect": "Allow",
      "Resource": "*"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "AmazonEKSClusterAutoscalerPolicy_Policy" {
  name = "AmazonEKSClusterAutoscalerPolicy"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "autoscaling:DescribeAutoScalingGroups",
        "autoscaling:DescribeAutoScalingInstances",
        "autoscaling:DescribeLaunchConfigurations",
        "autoscaling:DescribeTags",
        "autoscaling:SetDesiredCapacity",
        "autoscaling:TerminateInstanceInAutoScalingGroup",
        "ec2:DescribeLaunchTemplateVersions"
      ],
      "Effect": "Allow",
      "Resource": "*"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "AmazonEKSPlasmicClusterDeploy_Policy" {
  description = "Deploys to plasmic-cluster eks"
  name        = "AmazonEKSPlasmicClusterDeploy"
  path        = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "eks:UpdateClusterVersion",
        "eks:DescribeCluster",
        "eks:UpdateClusterConfig"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:eks:us-west-2:939375546786:cluster/plasmic-cluster",
      "Sid": "VisualEditor0"
    },
    {
      "Action": "eks:ListClusters",
      "Effect": "Allow",
      "Resource": "*",
      "Sid": "VisualEditor1"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "AmazonEKS_EBS_CSI_Driver_Policy_Policy" {
  name = "AmazonEKS_EBS_CSI_Driver_Policy"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "ec2:CreateSnapshot",
        "ec2:AttachVolume",
        "ec2:DetachVolume",
        "ec2:ModifyVolume",
        "ec2:DescribeAvailabilityZones",
        "ec2:DescribeInstances",
        "ec2:DescribeSnapshots",
        "ec2:DescribeTags",
        "ec2:DescribeVolumes",
        "ec2:DescribeVolumesModifications"
      ],
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": [
        "ec2:CreateTags"
      ],
      "Condition": {
        "StringEquals": {
          "ec2:CreateAction": [
            "CreateVolume",
            "CreateSnapshot"
          ]
        }
      },
      "Effect": "Allow",
      "Resource": [
        "arn:aws:ec2:*:*:volume/*",
        "arn:aws:ec2:*:*:snapshot/*"
      ]
    },
    {
      "Action": [
        "ec2:DeleteTags"
      ],
      "Effect": "Allow",
      "Resource": [
        "arn:aws:ec2:*:*:volume/*",
        "arn:aws:ec2:*:*:snapshot/*"
      ]
    },
    {
      "Action": [
        "ec2:CreateVolume"
      ],
      "Condition": {
        "StringLike": {
          "aws:RequestTag/ebs.csi.aws.com/cluster": "true"
        }
      },
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": [
        "ec2:CreateVolume"
      ],
      "Condition": {
        "StringLike": {
          "aws:RequestTag/CSIVolumeName": "*"
        }
      },
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": [
        "ec2:DeleteVolume"
      ],
      "Condition": {
        "StringLike": {
          "ec2:ResourceTag/CSIVolumeName": "*"
        }
      },
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": [
        "ec2:DeleteVolume"
      ],
      "Condition": {
        "StringLike": {
          "ec2:ResourceTag/ebs.csi.aws.com/cluster": "true"
        }
      },
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": [
        "ec2:DeleteSnapshot"
      ],
      "Condition": {
        "StringLike": {
          "ec2:ResourceTag/CSIVolumeSnapshotName": "*"
        }
      },
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": [
        "ec2:DeleteSnapshot"
      ],
      "Condition": {
        "StringLike": {
          "ec2:ResourceTag/ebs.csi.aws.com/cluster": "true"
        }
      },
      "Effect": "Allow",
      "Resource": "*"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "FullElasticContainerRegistry_Policy" {
  name = "FullElasticContainerRegistry"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": "ecr:*",
      "Effect": "Allow",
      "Resource": "*",
      "Sid": "VisualEditor0"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "GrafanaS3FullAccess_Policy" {
  name = "GrafanaS3FullAccess"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "s3:*"
      ],
      "Effect": "Allow",
      "Resource": [
        "arn:aws:s3:::plasmic-grafana",
        "arn:aws:s3:::plasmic-grafana/*"
      ],
      "Sid": "S3FullAccess"
    },
    {
      "Action": [
        "xray:BatchGetTraces",
        "xray:GetTraceSummaries",
        "xray:GetTraceGraph",
        "xray:GetGroups",
        "xray:GetTimeSeriesServiceStatistics",
        "xray:GetInsightSummaries",
        "xray:GetInsight",
        "xray:GetServiceGraph",
        "ec2:DescribeRegions"
      ],
      "Effect": "Allow",
      "Resource": "*"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "IAMManageOwnAccessKeys_Policy" {
  name = "IAMManageOwnAccessKeys"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "iam:DeleteAccessKey",
        "iam:GetAccessKeyLastUsed",
        "iam:UpdateAccessKey",
        "iam:GetUser",
        "iam:CreateAccessKey",
        "iam:ListAccessKeys"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:iam::*:user/$${aws:username}",
      "Sid": "VisualEditor0"
    },
    {
      "Action": "*",
      "Effect": "Allow",
      "Resource": "*"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "IAMManageOwnCredentials_Policy" {
  name = "IAMManageOwnCredentials"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "iam:GetAccountPasswordPolicy",
        "iam:ListVirtualMFADevices"
      ],
      "Effect": "Allow",
      "Resource": "*",
      "Sid": "AllowViewAccountInfo"
    },
    {
      "Action": [
        "iam:ChangePassword",
        "iam:GetUser"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:iam::*:user/$${aws:username}",
      "Sid": "AllowManageOwnPasswords"
    },
    {
      "Action": [
        "iam:CreateAccessKey",
        "iam:DeleteAccessKey",
        "iam:ListAccessKeys",
        "iam:UpdateAccessKey"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:iam::*:user/$${aws:username}",
      "Sid": "AllowManageOwnAccessKeys"
    },
    {
      "Action": [
        "iam:DeleteSigningCertificate",
        "iam:ListSigningCertificates",
        "iam:UpdateSigningCertificate",
        "iam:UploadSigningCertificate"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:iam::*:user/$${aws:username}",
      "Sid": "AllowManageOwnSigningCertificates"
    },
    {
      "Action": [
        "iam:DeleteSSHPublicKey",
        "iam:GetSSHPublicKey",
        "iam:ListSSHPublicKeys",
        "iam:UpdateSSHPublicKey",
        "iam:UploadSSHPublicKey"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:iam::*:user/$${aws:username}",
      "Sid": "AllowManageOwnSSHPublicKeys"
    },
    {
      "Action": [
        "iam:CreateServiceSpecificCredential",
        "iam:DeleteServiceSpecificCredential",
        "iam:ListServiceSpecificCredentials",
        "iam:ResetServiceSpecificCredential",
        "iam:UpdateServiceSpecificCredential"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:iam::*:user/$${aws:username}",
      "Sid": "AllowManageOwnGitCredentials"
    },
    {
      "Action": [
        "iam:CreateVirtualMFADevice",
        "iam:DeleteVirtualMFADevice"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:iam::*:mfa/$${aws:username}",
      "Sid": "AllowManageOwnVirtualMFADevice"
    },
    {
      "Action": [
        "iam:DeactivateMFADevice",
        "iam:EnableMFADevice",
        "iam:ListMFADevices",
        "iam:ResyncMFADevice"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:iam::*:user/$${aws:username}",
      "Sid": "AllowManageOwnUserMFA"
    },
    {
      "Condition": {
        "BoolIfExists": {
          "aws:MultiFactorAuthPresent": "false"
        }
      },
      "Effect": "Deny",
      "NotAction": [
        "iam:CreateVirtualMFADevice",
        "iam:EnableMFADevice",
        "iam:GetUser",
        "iam:ListMFADevices",
        "iam:ListVirtualMFADevices",
        "iam:ResyncMFADevice",
        "sts:GetSessionToken"
      ],
      "Resource": "*",
      "Sid": "DenyAllExceptListedIfNoMFA"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "IAMPassRole_Policy" {
  description = "Allows editing and creating EC2 Auto Scaling resources with an instance profile"
  name        = "IAMPassRole"
  path        = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": "iam:PassRole",
      "Effect": "Allow",
      "Resource": "*",
      "Sid": "VisualEditor0"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "JenkinsAgents_Policy" {
  description = "Allows jenkins master to start and stop agents"
  name        = "JenkinsAgents"
  path        = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "ec2:DescribeSpotInstanceRequests",
        "ec2:CancelSpotInstanceRequests",
        "ec2:GetConsoleOutput",
        "ec2:RequestSpotInstances",
        "ec2:RunInstances",
        "ec2:StartInstances",
        "ec2:StopInstances",
        "ec2:TerminateInstances",
        "ec2:CreateTags",
        "ec2:DeleteTags",
        "ec2:DescribeInstances",
        "ec2:DescribeInstanceTypes",
        "ec2:DescribeKeyPairs",
        "ec2:DescribeRegions",
        "ec2:DescribeImages",
        "ec2:DescribeAvailabilityZones",
        "ec2:DescribeSecurityGroups",
        "ec2:DescribeSubnets",
        "iam:ListInstanceProfilesForRole",
        "iam:PassRole",
        "ec2:GetPasswordData"
      ],
      "Effect": "Allow",
      "Resource": "*",
      "Sid": "Stmt1312295543082"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "PrometheusS3_Policy" {
  name = "PrometheusS3"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "s3:ListBucket",
        "s3:GetObject",
        "s3:DeleteObject",
        "s3:PutObject"
      ],
      "Effect": "Allow",
      "Resource": [
        "arn:aws:s3:::plasmic-prometheus-metrics/*",
        "arn:aws:s3:::plasmic-prometheus-metrics"
      ],
      "Sid": "Statement"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "S3ManageClips_Policy" {
  name = "S3ManageClips"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "s3:PutObject",
        "s3:GetObject"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:s3:::plasmic-clips/*",
      "Sid": "VisualEditor0"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "S3ManageListUploadedSiteAssets_Policy" {
  name = "S3ManageListUploadedSiteAssets"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "s3:PutObject",
        "s3:GetObject",
        "s3:ListBucket",
        "s3:PutObjectAcl"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:s3:::plasmic-site-assets/*",
      "Sid": "VisualEditor0"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "S3ManageUploadedSiteAssets_Policy" {
  description = "Manage uploaded image assets"
  name        = "S3ManageUploadedSiteAssets"
  path        = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "s3:PutObject",
        "s3:GetObject",
        "s3:PutObjectAcl"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:s3:::plasmic-site-assets/*",
      "Sid": "VisualEditor0"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "VantaAdditionalPermissions_Policy" {
  name = "VantaAdditionalPermissions"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "ecr:DescribeImageScanFindings",
        "ecr:DescribeImages",
        "ecr:ListTagsForResource",
        "ecr:BatchGetRepositoryScanningConfiguration",
        "inspector2:ListCoverage",
        "dynamodb:ListTagsOfResource",
        "inspector2:BatchGet*",
        "inspector2:Get*",
        "inspector2:Describe*",
        "inspector2:List*",
        "sqs:ListQueueTags"
      ],
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": [
        "datapipeline:EvaluateExpression",
        "datapipeline:QueryObjects",
        "rds:DownloadDBLogFilePortion"
      ],
      "Effect": "Deny",
      "Resource": "*"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "certbotdnsroute53_Policy" {
  name = "certbot-dns-route53"
  path = "/"

  policy = <<POLICY
{
  "Id": "certbot-dns-route53 policy",
  "Statement": [
    {
      "Action": [
        "route53:ListHostedZones",
        "route53:GetChange"
      ],
      "Effect": "Allow",
      "Resource": [
        "*"
      ]
    },
    {
      "Action": [
        "route53:ChangeResourceRecordSets"
      ],
      "Effect": "Allow",
      "Resource": [
        "arn:aws:route53:::hostedzone/Z2ODB6LEHQTNVF"
      ]
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}

resource "aws_iam_policy" "s3bucketaccessyang_Policy" {
  description = "s3 access policy for yang"
  name        = "s3-bucket-access-yang"
  path        = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "s3:GetBucketLocation",
        "s3:ListAllMyBuckets"
      ],
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": "s3:ListBucket",
      "Effect": "Allow",
      "Resource": [
        "arn:aws:s3:::plasmic-ray",
        "arn:aws:s3:::plasmic-static1",
        "arn:aws:s3:::plasmic-cypress",
        "arn:aws:s3:::plasmic-page-templates-thumbs",
        "arn:aws:s3:::plasmic-site-assets",
        "arn:aws:s3:::plasmic-discourse-backups",
        "arn:aws:s3:::plasmic-discourse-uploads"
      ]
    },
    {
      "Action": [
        "s3:*"
      ],
      "Effect": "Allow",
      "Resource": [
        "arn:aws:s3:::plasmic-ray/*",
        "arn:aws:s3:::plasmic-static1/*",
        "arn:aws:s3:::plasmic-cypress/*",
        "arn:aws:s3:::plasmic-page-templates-thumbs/*",
        "arn:aws:s3:::plasmic-site-assets/*",
        "arn:aws:s3:::plasmic-discourse-backups/*",
        "arn:aws:s3:::plasmic-discourse-uploads/*"
      ]
    }
  ],
  "Version": "2012-10-17"
}
POLICY

  tags = {
    Name = "aws-s3-policy-yang"
  }

  tags_all = {
    Name = "aws-s3-policy-yang"
  }
}

resource "aws_iam_policy" "s3discoursepolicy_Policy" {
  name = "s3-discourse-policy"
  path = "/"

  policy = <<POLICY
{
  "Statement": [
    {
      "Action": [
        "s3:List*",
        "s3:Get*",
        "s3:AbortMultipartUpload",
        "s3:DeleteObject",
        "s3:PutObject",
        "s3:PutObjectAcl",
        "s3:PutObjectVersionAcl",
        "s3:PutLifecycleConfiguration",
        "s3:CreateBucket",
        "s3:PutBucketCORS"
      ],
      "Effect": "Allow",
      "Resource": [
        "arn:aws:s3:::plasmic-discourse-uploads",
        "arn:aws:s3:::plasmic-discourse-uploads/*",
        "arn:aws:s3:::plasmic-discourse-backups",
        "arn:aws:s3:::plasmic-discourse-backups/*"
      ]
    },
    {
      "Action": [
        "s3:ListAllMyBuckets",
        "s3:HeadBucket"
      ],
      "Effect": "Allow",
      "Resource": "*"
    }
  ],
  "Version": "2012-10-17"
}
POLICY
}
