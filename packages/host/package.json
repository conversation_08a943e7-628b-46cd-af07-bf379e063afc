{"name": "@plasmicapp/host", "version": "1.0.219", "description": "plasmic library for app hosting", "main": "dist/index.cjs.js", "types": "dist/index.d.ts", "module": "dist/host.esm.js", "nx": {"targets": {"build": {"inputs": ["{projectRoot}/**/*", "!{projectRoot}/**/dist/**/*", "!{projectRoot}/src/version.ts"], "outputs": ["{projectRoot}/**/dist/**/*", "{projectRoot}/src/version.ts"]}}}, "files": ["dist", "registerComponent", "registerFunction", "registerGlobalContext", "registerToken", "registerTrait"], "size-limit": [{"path": "dist/host.esm.js", "limit": "3 KB"}], "scripts": {"build": "./update_version.sh && rollup -c", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "eslint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "dependencies": {"@plasmicapp/query": "0.1.79", "csstype": "^3.1.2", "window-or-global": "^1.0.1"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.2", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.1.0", "@types/classnames": "^2.3.0", "@types/node": "^20.3.3", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^3.26.1", "rollup-plugin-banner2": "^1.2.2", "rollup-plugin-dts": "^5.3.0", "rollup-plugin-typescript2": "^0.35.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}