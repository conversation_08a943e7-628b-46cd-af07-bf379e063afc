{"version": "0.1.183", "license": "MIT", "types": "./dist/index.d.ts", "main": "./dist/index.js", "module": "./dist/index.esm.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}}, "files": ["dist"], "engines": {"node": ">=10"}, "scripts": {"build": "yarn build:types && yarn build:index", "build:types": "yarn tsc", "build:index": "node ../../build.mjs ./src/index.tsx", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "eslint", "prepublishOnly": "npm run build", "postpublish": "bash ../../scripts/publish-api-doc-model.sh", "size": "size-limit", "analyze": "size-limit --why"}, "peerDependencies": {"react": ">=16.8.0"}, "name": "@plasmicapp/data-sources", "author": "<PERSON>", "size-limit": [{"path": "dist/index.js", "limit": "10 KB"}], "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "react": "^18.2.0", "react-dom": "^18.2.0"}, "dependencies": {"@plasmicapp/data-sources-context": "0.1.21", "@plasmicapp/host": "1.0.219", "@plasmicapp/isomorphic-unfetch": "1.0.3", "@plasmicapp/query": "0.1.79", "fast-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}}