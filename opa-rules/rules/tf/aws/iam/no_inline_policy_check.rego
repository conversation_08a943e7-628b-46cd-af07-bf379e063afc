package rules.tf_aws_iam_no_inline_policy_check

import data.fugue

__rego__metadoc__ := {
	"id": "SOC2_R0028",
	"title": "IAM role and user should not have inline policies",
	"description": "Checks that inline policy feature is not in use. The rule is NON_COMPLIANT if an AWS Identity and Access Management (IAM) user, IAM role or IAM group has any inline policy.",
	"custom": {"severity": "Low"},
}

resource_type = "MULTIPLE"

inline_resource_types = {
	"aws_iam_role",
	"aws_iam_role_policy",
	"aws_iam_user_policy",
	"aws_iam_group_policy",
}

inline_resources[id] = resource {
	some resource_type
	inline_resource_types[resource_type]
	resources = fugue.resources(resource_type)
	resource = resources[id]
}

filter_empty_policies(arr) = arr2 {
	arr2 = [p | p := arr[_]; p.policy != ""]
}

has_inline_policy(resource) {
	count(filter_empty_policies(resource.inline_policy)) > 0
}

has_inline_policy(resource) {
	resource.policy
}

policy[p] {
	resource = inline_resources[_]
	has_inline_policy(resource)
	p = fugue.deny_resource(resource)
}

policy[p] {
	resource = inline_resources[_]
	not has_inline_policy(resource)
	p = fugue.allow_resource(resource)
}
