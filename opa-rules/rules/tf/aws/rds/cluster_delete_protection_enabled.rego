package rules.tf_aws_rds_cluster_delete_protection_enabled

import data.fugue
import future.keywords.in

__rego__metadoc__ := {
	"custom": {"severity": "Medium"},
	"description": "Checks if an Amazon Relational Database Service (Amazon RDS) instance has deletion protection enabled",
	"id": "SOC2_R0038",
	"title": "Ensure RDS instance has deletion protection enabled",
}

resource_type := "aws_rds_cluster"

default allow = false

allow {
	input.deletion_protection == true
}
