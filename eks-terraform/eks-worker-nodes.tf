resource "aws_iam_role" "plasmic_cluster_nodes" {
  name               = "${var.cluster_name}-${var.env_code}-node-role"
  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
  {
    "Effect": "Allow",
    "Principal": {
      "Service": "ec2.amazonaws.com"
    },
    "Action": "sts:AssumeRole"
    }
  ]
  }
  POLICY
}

resource "aws_iam_role_policy_attachment" "node_AmazonEKSWorkerPolicy" {
  policy_arn = "arn:aws:iam:aws:policy/AmazonEKSWorkerNodePolicy"
  role       = aws_iam_role.plasmic_cluster_nodes.name
}

resource "aws_iam_role_policy_attachment" "node_AmazonEKS_CNI_Policy" {
  policy_arn = "arn:aws:iam:aws:policy/AmazonEKS_CNI_policy"
  role       = aws_iam_role.plasmic_cluster_nodes.name
}

resource "aws_iam_role_policy_attachment" "node_AmazonEC2ContainerRegistryReadOnly" {
  policy_arn = "arn:aws:iam:aws:policy/AmazonEC2ContainerRegistryReadOnly"
  role       = aws_iam_role.plasmic_cluster_nodes.name
}

resource "aws_eks_node_group" "plasmic_cluster_node_group" {
  cluster_name    = aws_eks_cluster.plasmic_cluster.name
  node_group_name = "${var.cluster_name}-${var.env_code}-node-group"
  node_role_arn   = aws_iam_role.plasmic_cluster_nodes.arn
  subnet_ids      = flatten([aws_subnet.cluster_subnet_private.*.id, aws_subnet.cluster_subnet_public.*.id])
  instance_types  = [var.node_type]
  scaling_config {
    desired_size = var.desired_size
    max_size     = var.max_size
    min_size     = var.min_size
  }
  depends_on = [
    aws_iam_role_policy_attachment.node_AmazonEC2ContainerRegistryReadOnly,
    aws_iam_role_policy_attachment.node_AmazonEKS_CNI_Policy,
    aws_iam_role_policy_attachment.node_AmazonEKSWorkerPolicy
  ]
}